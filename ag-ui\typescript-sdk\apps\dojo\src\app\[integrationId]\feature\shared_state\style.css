.copilotKitWindow {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.copilotKitHeader {
  border-top-left-radius: 5px !important;
  background-color: #fff;
  color: #000;
  border-bottom: 0px;
}

/* Recipe App Styles */
.app-container {
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  position: relative;
  overflow: auto;
}

.recipe-card {
  background-color: rgba(255, 255, 255, 0.97);
  border-radius: 16px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25), 0 5px 15px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 750px;
  margin: 20px auto;
  padding: 14px 32px;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  animation: fadeIn 0.5s ease-out forwards;
  box-sizing: border-box;
  overflow: hidden;
}

.recipe-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Recipe Header */
.recipe-header {
  margin-bottom: 24px;
}

.recipe-title-input {
  width: 100%;
  font-size: 24px;
  font-weight: bold;
  border: none;
  outline: none;
  padding: 8px 0;
  margin-bottom: 0px;
}

.recipe-meta {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 5px;
  margin-bottom: 14px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #555;
}

.meta-icon {
  font-size: 20px;
  color: #777;
}

.meta-text {
  font-size: 15px;
}

/* Recipe Meta Selects */
.meta-item select {
  border: none;
  background: transparent;
  font-size: 15px;
  color: #555;
  cursor: pointer;
  outline: none;
  padding-right: 18px;
  transition: color 0.2s, transform 0.1s;
  font-weight: 500;
}

.meta-item select:hover,
.meta-item select:focus {
  color: #FF5722;
}

.meta-item select:active {
  transform: scale(0.98);
}

.meta-item select option {
  color: #333;
  background-color: white;
  font-weight: normal;
  padding: 8px;
}

/* Section Container */
.section-container {
  margin-bottom: 20px;
  position: relative;
  width: 100%;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #333;
  position: relative;
  display: inline-block;
}

.section-title:after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #ff7043;
  border-radius: 3px;
}

/* Dietary Preferences */
.dietary-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px 16px;
  margin-bottom: 16px;
  width: 100%;
}

.dietary-option {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  cursor: pointer;
  margin-bottom: 4px;
}

.dietary-option input {
  cursor: pointer;
}

/* Ingredients */
.ingredients-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
  width: 100%;
  box-sizing: border-box;
}

.ingredient-card {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  position: relative;
  transition: all 0.2s ease;
  border: 1px solid rgba(240, 240, 240, 0.8);
  width: calc(33.333% - 7px);
  box-sizing: border-box;
}

.ingredient-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12);
}

.ingredient-card .remove-button {
  position: absolute;
  right: 10px;
  top: 10px;
  background: none;
  border: none;
  color: #ccc;
  font-size: 16px;
  cursor: pointer;
  display: none;
  padding: 0;
  width: 24px;
  height: 24px;
  line-height: 1;
}

.ingredient-card:hover .remove-button {
  display: block;
}

.ingredient-icon {
  font-size: 24px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #f7f7f7;
  border-radius: 50%;
  flex-shrink: 0;
}

.ingredient-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 3px;
  min-width: 0;
}

.ingredient-name-input,
.ingredient-amount-input {
  border: none;
  background: transparent;
  outline: none;
  width: 100%;
  padding: 0;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.ingredient-name-input {
  font-weight: 500;
  font-size: 14px;
}

.ingredient-amount-input {
  font-size: 13px;
  color: #666;
}

.ingredient-name-input::placeholder,
.ingredient-amount-input::placeholder {
  color: #aaa;
}

.remove-button {
  background: none;
  border: none;
  color: #999;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}

.remove-button:hover {
  color: #FF5722;
}

/* Instructions */
.instructions-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
  position: relative;
  margin-bottom: 12px;
  width: 100%;
}

.instruction-item {
  position: relative;
  display: flex;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 8px;
  align-items: flex-start;
}

.instruction-number {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 26px;
  height: 26px;
  background-color: #ff7043;
  color: white;
  border-radius: 50%;
  font-weight: 600;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(255, 112, 67, 0.3);
  z-index: 1;
  font-size: 13px;
  margin-top: 2px;
}

.instruction-line {
  position: absolute;
  left: 13px; /* Half of the number circle width */
  top: 22px;
  bottom: -18px;
  width: 2px;
  background: linear-gradient(to bottom, #ff7043 60%, rgba(255, 112, 67, 0.4));
  z-index: 0;
}

.instruction-content {
  background-color: white;
  border-radius: 10px;
  padding: 10px 14px;
  margin-left: 12px;
  flex-grow: 1;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(240, 240, 240, 0.8);
  position: relative;
  width: calc(100% - 38px);
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.instruction-content-editing {
  background-color: #fff9f6;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12), 0 0 0 2px rgba(255, 112, 67, 0.2);
}

.instruction-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.instruction-textarea {
  width: 100%;
  background: transparent;
  border: none;
  resize: vertical;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
  min-height: 20px;
  outline: none;
  padding: 0;
  margin: 0;
}

.instruction-delete-btn {
  position: absolute;
  background: none;
  border: none;
  color: #ccc;
  font-size: 16px;
  cursor: pointer;
  display: none;
  padding: 0;
  width: 20px;
  height: 20px;
  line-height: 1;
  top: 50%;
  transform: translateY(-50%);
  right: 8px;
}

.instruction-content:hover .instruction-delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Action Button */
.action-container {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  padding-bottom: 20px;
  position: relative;
}

.improve-button {
  background-color: #ff7043;
  border: none;
  color: white;
  border-radius: 30px;
  font-size: 18px;
  font-weight: 600;
  padding: 14px 28px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 112, 67, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  min-width: 180px;
}

.improve-button:hover {
  background-color: #ff5722;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 112, 67, 0.5);
}

.improve-button.loading {
  background-color: #ff7043;
  opacity: 0.8;
  cursor: not-allowed;
  padding-left: 42px; /* Reduced padding to bring text closer to icon */
  padding-right: 22px; /* Balance the button */
  justify-content: flex-start; /* Left align text for better alignment with icon */
}

.improve-button.loading:after {
  content: ""; /* Add space between icon and text */
  display: inline-block;
  width: 8px; /* Width of the space */
}

.improve-button:before {
  content: "";
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83'/%3E%3C/svg%3E");
  width: 20px; /* Slightly smaller icon */
  height: 20px;
  background-repeat: no-repeat;
  background-size: contain;
  position: absolute;
  left: 16px; /* Slightly adjusted */
  top: 50%;
  transform: translateY(-50%);
  display: none;
}

.improve-button.loading:before {
  display: block;
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

/* Ping Animation */
.ping-animation {
  position: absolute;
  display: flex;
  width: 12px;
  height: 12px;
  top: 0;
  right: 0;
}

.ping-circle {
  position: absolute;
  display: inline-flex;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #38BDF8;
  opacity: 0.75;
  animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.ping-dot {
  position: relative;
  display: inline-flex;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #0EA5E9;
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Instruction hover effects */
.instruction-item:hover .instruction-delete-btn {
  display: flex !important;
}

/* Add some subtle animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Better center alignment for the recipe card */
.recipe-card-container {
  display: flex;
  justify-content: center;
  width: 100%;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
}

/* Add Buttons */
.add-button {
  background-color: transparent;
  color: #FF5722;
  border: 1px dashed #FF5722;
  border-radius: 8px;
  padding: 10px 16px;
  cursor: pointer;
  font-weight: 500;
  display: inline-block;
  font-size: 14px;
  margin-bottom: 0;
}

.add-step-button {
  background-color: transparent;
  color: #FF5722;
  border: 1px dashed #FF5722;
  border-radius: 6px;
  padding: 6px 12px;
  cursor: pointer;
  font-weight: 500;
  font-size: 13px;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}