FROM centos:8

# 设置时区和语言环境
ENV TZ=Asia/Shanghai
ENV LANG=en_US.UTF-8
ENV LC_ALL=en_US.UTF-8

# 安装EPEL仓库
RUN sed -i 's/mirrorlist/#mirrorlist/g' /etc/yum.repos.d/CentOS-* && \
    sed -i 's|#baseurl=http://mirror.centos.org|baseurl=http://vault.centos.org|g' /etc/yum.repos.d/CentOS-* && \
    yum update -y && \
    yum install -y epel-release

# 安装Python 3.10
RUN yum install -y python3 python3-pip python3-devel && \
    yum clean all

# 升级pip到最新版本
RUN python3 -m pip install --upgrade pip

# 创建软链接
RUN ln -sf /usr/bin/python3 /usr/bin/python && \
    ln -sf /usr/bin/pip3 /usr/bin/pip

# 验证安装
RUN python3 --version && \
    pip3 --version

# 设置工作目录
WORKDIR /app

# 默认命令
CMD ["/bin/bash"]