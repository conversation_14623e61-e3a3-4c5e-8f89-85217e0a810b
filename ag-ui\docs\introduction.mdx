---
title: Introduction
description: "Get started with the Agent User Interaction Protocol (AG-UI)"
---

**AG-UI** standardizes how **front-end applications connect to AI agents**
through an open protocol. Think of it as a universal translator for AI-driven
systems- no matter what language an agent speaks: **AG-UI ensures fluent
communication**.

## Why AG-UI?

AG-UI helps developers build next-generation AI workflows that need **real-time
interactivity**, **live state streaming** and **human-in-the-loop
collaboration**.

AG-UI provides:

- **A straightforward approach** to integrating AI agents with the front-end
  through frameworks such as
  [CopilotKit 🪁](https://github.com/CopilotKit/CopilotKit)
- **Building blocks** for an efficient wire protocol for human⚡️agent
  communication
- **Best practices** for chat, streaming state updates, human-in-the-loop and
  shared state

## Existing Integrations

AG-UI has been integrated with several popular agent frameworks, making it easy
to adopt regardless of your preferred tooling:

- **[LangGraph](https://docs.copilotkit.ai/coagents)**: Build agent-native applications with shared state and human-in-the-loop workflows using LangGraph's powerful orchestration capabilities.
- **[<PERSON><PERSON>](/mastra)**: Leverage TypeScript for building strongly-typed agent implementations with enhanced developer experience.
- **[Pydantic AI](https://docs.copilotkit.ai/pydantic-ai)**: Painlessly build production grade agentic applications and workflows using fully type-safe Python.
- **[CrewAI Flows](https://docs.copilotkit.ai/crewai-flows)**: Create sequential multi-agent workflows with well-defined stages and process control.
- **[CrewAI Crews](https://docs.copilotkit.ai/crewai-crews)**: Design collaborative agent teams with specialized roles and inter-agent communication.
- **[Agno](https://docs.copilotkit.ai/agno)**: Build, run and manage secure multi-agent systems in your cloud with Agno's AgentOS.
- **[LlamaIndex](https://docs.copilotkit.ai/llamaindex)**: A simple, flexible framework for building agentic generative AI applications that allow large language models to work with your data in any format.
- **[AG2](/ag2)**: Utilize the open-source AgentOS for scalable,
  production-ready agent deployments.

These integrations make it straightforward to connect your preferred agent
framework with frontend applications through the AG-UI protocol.

### Architecture

At its core, AG-UI bridges AI agents and front-end applications using a
lightweight, event-driven protocol:

```mermaid
flowchart LR
    subgraph "Frontend"
        FE["Front-end"]
    end

    subgraph "Backend"
        A1["AI Agent A"]
        P["Secure Proxy"]
        A2["AI Agent B"]
        A3["AI Agent C"]
    end

    FE <-->|"AG-UI Protocol"| A1
    FE <-->|"AG-UI Protocol"| P
    P <-->|"AG-UI Protocol"| A2
    P <-->|"AG-UI Protocol"| A3

    class P mintStyle;
    classDef mintStyle fill:#E0F7E9,stroke:#66BB6A,stroke-width:2px,color:#000000;

    %% Apply slight border radius to each node
    style FE rx:5, ry:5;
    style A1 rx:5, ry:5;
    style P rx:5, ry:5;
    style A2 rx:5, ry:5;
    style A3 rx:5, ry:5;
```

- **Front-end**: The application (chat or any AI-enabled app) that communicates
  over AG-UI
- **AI Agent A**: An agent that the front-end can connect to directly without
  going through the proxy
- **Secure Proxy**: An intermediary proxy that securely routes requests from the
  front-end to multiple AI agents
- **Agents B and C**: Agents managed by the proxy service

## Technical Overview

AG-UI is designed to be lightweight and minimally opinionated, making it easy to
integrate with a wide range of agent implementations. The protocol's flexibility
comes from its simple requirements:

1. **Event-Driven Communication**: Agents need to emit any of the 16
   standardized event types during execution, creating a stream of updates that
   clients can process.

2. **Bidirectional Interaction**: Agents accept input from users, enabling
   collaborative workflows where humans and AI work together seamlessly.

The protocol includes a built-in middleware layer that maximizes compatibility
in two key ways:

- **Flexible Event Structure**: Events don't need to match AG-UI's format
  exactly—they just need to be AG-UI-compatible. This allows existing agent
  frameworks to adapt their native event formats with minimal effort.

- **Transport Agnostic**: AG-UI doesn't mandate how events are delivered,
  supporting various transport mechanisms including Server-Sent Events (SSE),
  webhooks, WebSockets, and more. This flexibility lets developers choose the
  transport that best fits their architecture.

This pragmatic approach makes AG-UI easy to adopt without requiring major
changes to existing agent implementations or frontend applications.

## Comparison with other protocols

AG-UI focuses explicitly and specifically on the agent-user interactivity layer.
It does not compete with protocols such as A2A (Agent-to-Agent protocol) and MCP
(Model Context Protocol).

For example, the same agent may communicate with another agent via A2A while
communicating with the user via AG-UI, and while calling tools provided by an
MCP server.

These protocols serve complementary purposes in the agent ecosystem:

- **AG-UI**: Handles human-in-the-loop interaction and streaming UI updates
- **A2A**: Facilitates agent-to-agent communication and collaboration
- **MCP**: Standardizes tool calls and context handling across different models

## Quick Start

Choose the path that fits your needs:

<CardGroup cols={2}>
 <Card
    title="AG-UI Middleware Connectors"
    icon="bolt"
    href="/quickstart/middleware"
    color="#3B82F6"
    iconType="solid"
  >
    Connect AG-UI with existing protocols, in process agents or custom solutions
    **using TypeScript**
  </Card>

  <Card
    title="AG-UI Compatible Servers"
    icon="wrench"
    href="/quickstart/server"
    color="#3B82F6"
    iconType="solid"
  >
    Implement AG-UI compatible servers **using Python or TypeScript**
  </Card>
 
</CardGroup>

## Resources

Explore guides, tools, and integrations to help you build, optimize, and extend
your AG-UI implementation. These resources cover everything from practical
development workflows to debugging techniques.

<CardGroup cols={2}>
  <Card
    title="Explore Integrations"
    icon="cubes"
    iconType="light"
    color="#3B82F6"
    href="/integrations"
  >
    Discover ready-to-use AG-UI integrations across popular agent frameworks and
    platforms
  </Card>
  <Card
    title="Developing with Cursor"
    icon="rocket"
    iconType="light"
    color="#3B82F6"
    href="/tutorials/cursor"
  >
    Use Cursor to build AG-UI implementations faster
  </Card>
  <Card
    title="Troubleshooting AG-UI"
    icon="bug"
    iconType="light"
    color="#3B82F6"
    href="/tutorials/debugging"
  >
    Fix common issues when working with AG-UI servers and clients
  </Card>
</CardGroup>

## Explore AG-UI

Dive deeper into AG-UI's core concepts and capabilities:

<CardGroup cols={2}>
  <Card
    title="Core architecture"
    icon="sitemap"
    iconType="light"
    color="#3B82F6"
    href="/docs/concepts/architecture"
  >
    Understand how AG-UI connects agents, protocols, and front-ends
  </Card>

  <Card
    title="Transports"
    icon="network-wired"
    iconType="light"
    color="#3B82F6"
    href="/docs/concepts/transports"
  >
    Learn about AG-UI's communication mechanism
  </Card>
</CardGroup>

## Contributing

Want to contribute? Check out our
[Contributing Guide](/development/contributing) to learn how you can help
improve AG-UI.

## Support and Feedback

Here's how to get help or provide feedback:

- For bug reports and feature requests related to the AG-UI specification, SDKs,
  or documentation (open source), please
  [create a GitHub issue](https://github.com/ag-ui-protocol)
- For discussions or Q&A about the AG-UI specification, use the
  [specification discussions](https://github.com/ag-ui-protocol/specification/discussions)
- For discussions or Q&A about other AG-UI open source components, use the
  [organization discussions](https://github.com/orgs/ag-ui-protocol/discussions)
