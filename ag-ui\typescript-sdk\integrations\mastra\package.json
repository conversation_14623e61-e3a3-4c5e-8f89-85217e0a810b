{"name": "@ag-ui/mastra", "version": "0.0.10", "license": "Apache-2.0", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "private": false, "publishConfig": {"access": "public"}, "files": ["dist/**", "README.md"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist .turbo node_modules", "typecheck": "tsc --noEmit", "test": "jest", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}, "dependencies": {"@ai-sdk/ui-utils": "^1.1.19", "@mastra/client-js": "^0.10.18", "rxjs": "7.8.1"}, "peerDependencies": {"@ag-ui/core": ">=0.0.37", "@ag-ui/client": ">=0.0.37", "@copilotkit/runtime": "^1.9.3", "@mastra/core": ">=0.11.1", "zod": "^3.25.67"}, "devDependencies": {"@ag-ui/core": "workspace:*", "@ag-ui/client": "workspace:*", "@mastra/core": "^0.13.0", "@types/jest": "^29.5.14", "@types/node": "^20.11.19", "jest": "^29.7.0", "ts-jest": "^29.1.2", "tsup": "^8.0.2", "typescript": "^5.3.3"}}