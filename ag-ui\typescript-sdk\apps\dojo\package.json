{"name": "demo-viewer", "version": "0.1.0", "private": true, "scripts": {"dev": "npm run generate-content-json && next dev", "build": "next build", "start": "npm run generate-content-json && next start", "lint": "next lint", "mastra:dev": "<PERSON>ra dev", "generate-content-json": "npx tsx scripts/generate-content-json.ts"}, "dependencies": {"@ag-ui/agno": "workspace:*", "@ag-ui/crewai": "workspace:*", "@ag-ui/langgraph": "workspace:*", "@ag-ui/llamaindex": "workspace:*", "@ag-ui/mastra": "workspace:*", "@ag-ui/middleware-starter": "workspace:*", "@ag-ui/pydantic-ai": "workspace:*", "@ag-ui/server-starter": "workspace:*", "@ag-ui/server-starter-all-features": "workspace:*", "@ag-ui/vercel-ai-sdk": "workspace:*", "@ai-sdk/openai": "^1.3.22", "@copilotkit/react-core": "1.10.1", "@copilotkit/react-ui": "1.10.1", "@copilotkit/runtime": "1.10.1", "@copilotkit/runtime-client-gql": "1.10.1", "@copilotkit/shared": "1.10.1", "@mastra/client-js": "^0.10.18", "@mastra/core": "^0.13.0", "@mastra/dynamodb": "^0.13.3", "@mastra/libsql": "^0.13.0", "@mastra/loggers": "^0.10.5", "@mastra/memory": "^0.12.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/mdx": "^3.1.0", "@mdx-js/react": "^3.1.0", "@monaco-editor/react": "^4.7.0", "@next/mdx": "^15.2.3", "@phosphor-icons/react": "^2.1.10", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/react-syntax-highlighter": "^15.5.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "diff": "^7.0.0", "fast-json-patch": "^3.1.1", "lucide-react": "^0.477.0", "markdown-it": "^14.1.0", "markdown-it-ins": "^4.0.0", "next": "15.2.1", "next-themes": "^0.4.6", "openai": "^4.98.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rxjs": "7.8.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.22.4"}, "peerDependencies": {"@ag-ui/client": "workspace:*", "@ag-ui/core": "workspace:*", "@ag-ui/encoder": "workspace:*", "@ag-ui/proto": "workspace:*"}, "devDependencies": {"@eslint/eslintrc": "^3", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/diff": "^7.0.1", "@types/markdown-it": "^14.1.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.2.0", "eslint": "^9", "eslint-config-next": "15.2.1", "tailwindcss": "^4", "tsx": "^4.7.0", "typescript": "^5", "wait-port": "^1.1.0"}}