---
title: Contributing
description: How to participate in Agent User Interaction Protocol development
---

# Naming conventions

Add your package under `typescript-sdk/integrations/` with docs and tests.

If your integration is work in progress, you can still add it to main branch.
You can prefix it with `wip-`, i.e.
(`typescript-sdk/integrations/wip-your-integration`) or if you're a third party
contributor use the `community` prefix, i.e.
(`typescript-sdk/integrations/community-your-integration`).

For questions and discussions, please use
[GitHub Discussions](https://github.com/orgs/ag-ui-protocol/discussions).
