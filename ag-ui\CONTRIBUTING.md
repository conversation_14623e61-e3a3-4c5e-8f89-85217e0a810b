# 👋 Contributing to AG-UI

Thanks for checking out AG-UI! Whether you’re here to fix a bug, ship a feature, improve the docs, or just figure out how things work—we’re glad you’re here.

Here’s how to get involved:

---

## 🤔 Have a Question or Ran Into Something?

Pick the right spot so we can help you faster:

- **🐛 Bugs / Feature Ideas** → [GitHub Issues](https://github.com/ag-ui-protocol/ag-ui/issues)  
- **❓ "How do I...?" / General Questions** → [GitHub Discussions](https://github.com/ag-ui-protocol/ag-ui/discussions)  
- **💬 Quick chats / casual stuff** → [Discord](https://discord.gg/Jd3FzfdJa8) → `#-💎-contributing`

---

## 🧑‍💻 Want to Contribute Code?

1. **Find Something to Work On**  
   Browse open issues on [GitHub](https://github.com/ag-ui-protocol/ag-ui/issues).  
   Got your own idea? Open an issue first so we can start the discussion.

2. **Ask to Be Assigned**  
   Comment on the issue and tag a code owner:  
   → [Code Owners](https://github.com/ag-ui-protocol/ag-ui/blob/main/.github/CODEOWNERS)

3. **Get on the Roadmap**  
   Once approved, you'll be assigned the issue, and it'll get added to our [roadmap](https://github.com/orgs/ag-ui-protocol/projects/1).

4. **Coordinate With Others**  
   - If you're collaborating or need feedback, start a thread in `#-💎-contributing` on Discord  
   - Or just DM the assignee directly

5. **Open a Pull Request**  
   - When you’re ready, submit your PR  
   - In the description, include: `Fixes #<issue-number>`  
     (This links your PR to the issue and closes it automatically)

6. **Review & Merge**  
   - A maintainer will review your code and leave comments if needed  
   - Once it’s approved, we’ll merge it and move the issue to “done.”

**NOTE:** All community integrations (ie, .NET, Golang SDK, etc.) will need to be maintained by the community

---

## 📝 Want to Contribute to the Docs?

Docs are part of the codebase and super valuable—thanks for helping improve them!

Here’s how to contribute:

1. **Open an Issue First**  
   - Open a [GitHub issue](https://github.com/ag-ui-protocol/ag-ui/issues) describing what you’d like to update or add.  
   - Then comment and ask to be assigned.

2. **Submit a PR**  
   - Once assigned, make your edits and open a pull request.
   - In the description, include: `Fixes #<issue-number>`  
     (This links your PR to the issue and closes it automatically)

   - A maintainer will review it and merge if it looks good.

That’s it! Simple and appreciated.

---

## 🙌 That’s It!

AG-UI is community-built, and every contribution helps shape where we go next.  
Big thanks for being part of it!
