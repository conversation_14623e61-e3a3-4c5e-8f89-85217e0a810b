#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
容量数据收集器配置文件
"""

import os
from datetime import time

# API配置
API_CONFIG = {
    'base_url': 'http://127.0.0.1:5002',  # app.py服务地址
    'timeout': 30,
    'retry_times': 3,
    'retry_delay': 5  # 重试间隔（秒）
}

# 数据库配置 (MySQL)
DATABASE_CONFIG = {
    'host': 'localhost',  # Docker MySQL在同一台服务器
    'port': 13306,        # Docker映射的端口
    'database': 'prod',   # 数据库名
    'user': 'mrms',       # 用户名
    'password': 'kfaccess', # 密码
    'charset': 'utf8mb4',
    'autocommit': False,
    'connect_timeout': 30
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'log_file': 'capacity_collector.log',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
    'format': '%(asctime)s - %(levelname)s - %(message)s'
}

# 收集任务配置
COLLECTION_CONFIG = {
    'api_call_delay': 2,  # API调用间隔（秒）
    'batch_size': 100,    # 批量插入大小
    'enable_cleanup': True,  # 是否启用数据清理
    'retention_days': 365,   # 数据保留天数
}

# 定时任务配置
SCHEDULE_CONFIG = {
    'daily_time': time(2, 0),  # 每日执行时间 02:00
    'enable_weekend': True,    # 是否在周末执行
    'enable_holiday': True,    # 是否在节假日执行
}

# 告警配置
ALERT_CONFIG = {
    'enable_email': False,
    'email_recipients': [],
    'enable_webhook': False,
    'webhook_url': '',
    'alert_on_failure': True,
    'alert_on_partial_success': True
}

# 健康检查配置
HEALTH_CHECK_CONFIG = {
    'enable': True,
    'check_interval': 300,  # 5分钟
    'api_health_endpoint': '/api/health'
}

# 环境变量覆盖
def load_from_env():
    """从环境变量加载配置"""
    
    # API配置
    if os.getenv('CAPACITY_API_URL'):
        API_CONFIG['base_url'] = os.getenv('CAPACITY_API_URL')
    if os.getenv('CAPACITY_API_TIMEOUT'):
        API_CONFIG['timeout'] = int(os.getenv('CAPACITY_API_TIMEOUT'))
    
    # 数据库配置
    if os.getenv('CAPACITY_DB_HOST'):
        DATABASE_CONFIG['host'] = os.getenv('CAPACITY_DB_HOST')
    if os.getenv('CAPACITY_DB_PORT'):
        DATABASE_CONFIG['port'] = int(os.getenv('CAPACITY_DB_PORT'))
    if os.getenv('CAPACITY_DB_NAME'):
        DATABASE_CONFIG['database'] = os.getenv('CAPACITY_DB_NAME')
    if os.getenv('CAPACITY_DB_USER'):
        DATABASE_CONFIG['user'] = os.getenv('CAPACITY_DB_USER')
    if os.getenv('CAPACITY_DB_PASSWORD'):
        DATABASE_CONFIG['password'] = os.getenv('CAPACITY_DB_PASSWORD')
    
    # 日志配置
    if os.getenv('CAPACITY_LOG_LEVEL'):
        LOGGING_CONFIG['level'] = os.getenv('CAPACITY_LOG_LEVEL')
    if os.getenv('CAPACITY_LOG_FILE'):
        LOGGING_CONFIG['log_file'] = os.getenv('CAPACITY_LOG_FILE')

# 加载环境变量配置
load_from_env()

# 验证配置
def validate_config():
    """验证配置有效性"""
    errors = []
    
    # 验证API配置
    if not API_CONFIG['base_url']:
        errors.append("API base_url 不能为空")
    
    # 验证数据库配置
    required_db_fields = ['host', 'port', 'database', 'user', 'password']
    for field in required_db_fields:
        if not DATABASE_CONFIG.get(field):
            errors.append(f"数据库配置 {field} 不能为空")
    
    if errors:
        raise ValueError("配置验证失败:\n" + "\n".join(errors))
    
    return True

# 获取配置信息
def get_config_info():
    """获取配置信息摘要"""
    return {
        'api_url': API_CONFIG['base_url'],
        'db_host': DATABASE_CONFIG['host'],
        'db_port': DATABASE_CONFIG['port'],
        'db_name': DATABASE_CONFIG['database'],
        'log_level': LOGGING_CONFIG['level'],
        'daily_time': SCHEDULE_CONFIG['daily_time'].strftime('%H:%M'),
        'retention_days': COLLECTION_CONFIG['retention_days']
    }
