app:
  description: 通过对话方式生成智能容量报告，支持多轮交互和组合查询
  icon: 💬
  icon_background: '#2563EB'
  mode: advanced-chat
  name: 智能容量报告系统(对话版)
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: langgenius/siliconflow:0.0.8@217f973bd7ced1b099c2f0c669f1356bdf4cc38b8372fd58d7874f9940b95de3
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: 🎉 欢迎使用智能容量报告系统(对话版)！

      ✨ 系统功能：
      🤖 智能对话交互：通过自然语言对话生成容量报告
      🔍 支持多种查询模式：
        • 完整容量报告（存储+数据库+容器+虚拟化）
        • 单项查询（存储、数据库、容器、虚拟化）
        • 组合查询（如：存储+虚拟化、数据库+容器等）
      🧠 LLM智能分析和风险评估
      📊 按规定格式生成专业报告
      📄 自动导出Word文档格式

      💬 对话优势：
      • 多轮交互：可以追问和补充需求
      • 实时反馈：即时查看分析结果
      • 灵活调整：可以修改查询范围
      • 历史记录：保留对话上下文

      📋 使用方式：
      直接告诉我您的需求，例如：
      • "我需要生成存储和虚拟化的容量报告"
      • "帮我分析一下数据库的容量情况"
      • "生成完整的IT基础设施容量报告"

      ⚡ 预计处理时间：2-4分钟
      📁 文档保存位置：./reports/ 目录
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 我需要生成一份完整的IT基础设施容量报告
    - 帮我检查存储和虚拟化的容量状况
    - 生成数据库容量专项分析报告
    - 我想了解容器集群的资源使用情况
    - 请分析存储、数据库、虚拟化三个方面的容量
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: start
        targetType: llm
      id: start-question-classifier
      source: start
      sourceHandle: source
      target: question-classifier
      targetHandle: target
      type: custom
    - data:
        sourceType: llm
        targetType: http-request
      id: question-classifier-get-data
      source: question-classifier
      sourceHandle: source
      target: get-capacity-data
      targetHandle: target
      type: custom
    - data:
        sourceType: http-request
        targetType: llm
      id: get-data-llm-analysis
      source: get-capacity-data
      sourceHandle: source
      target: llm-analysis
      targetHandle: target
      type: custom
    - data:
        sourceType: llm
        targetType: http-request
      id: llm-analysis-export-word
      source: llm-analysis
      sourceHandle: source
      target: export-word
      targetHandle: target
      type: custom
    - data:
        sourceType: http-request
        targetType: answer
      id: export-word-answer
      source: export-word
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
    nodes:
    - data:
        desc: 对话开始节点
        selected: false
        title: 开始
        type: start
        variables: []
      height: 89
      id: start
      position:
        x: -50
        y: 114.5090375355316
      positionAbsolute:
        x: -50
        y: 114.5090375355316
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: 分析用户需求，自动判断应该生成哪种类型的容量报告
        model:
          completion_params:
            temperature: 0.1
          mode: chat
          name: gpt-4
          provider: openai
        prompt_template:
        - id: system-prompt
          role: system
          text: |
            你是一个专业的IT容量报告需求分析专家，负责分析用户的自然语言需求，并判断应该生成哪种类型的容量报告。

            ## 支持的报告类型：
            1. **all** - 完整容量报告（包含存储、数据库、容器、虚拟化所有维度）
            2. **storage** - 存储容量专项报告
            3. **database** - 数据库容量专项报告
            4. **container** - 容器容量专项报告
            5. **virtualization** - 虚拟化容量专项报告
            6. **组合类型** - 多个维度的组合报告，用逗号分隔，如：storage,virtualization

            ## 分析规则：
            - 如果用户提到"完整"、"全面"、"所有"、"整体"等词汇，选择 **all**
            - 如果用户明确提到"存储"、"磁盘"、"硬盘"等，包含 **storage**
            - 如果用户明确提到"数据库"、"DB"、"MySQL"、"Oracle"等，包含 **database**
            - 如果用户明确提到"容器"、"Docker"、"Kubernetes"、"K8s"等，包含 **container**
            - 如果用户明确提到"虚拟化"、"虚拟机"、"VM"、"ESX"、"vSphere"等，包含 **virtualization**
            - 如果用户提到多个维度，用逗号分隔组合，如：storage,virtualization
            - 如果需求不明确，默认选择 **all**

            ## 输出格式：
            你必须严格按照以下JSON格式输出，不要包含任何其他内容：
            ```json
            {
              "query_type": "报告类型",
              "analysis": "需求分析说明",
              "confidence": "置信度(0-1)",
              "report_sections": ["章节列表"]
            }
            ```

            ## 示例：
            示例1 - 单项查询：
            用户需求："我需要检查存储系统的容量使用情况"
            输出：
            ```json
            {
              "query_type": "storage",
              "analysis": "用户明确要求检查存储系统容量，应生成存储专项报告",
              "confidence": 0.95,
              "report_sections": ["storage"]
            }
            ```

            示例2 - 组合查询：
            用户需求："我需要同时分析存储和虚拟化的容量状况"
            输出：
            ```json
            {
              "query_type": "storage,virtualization",
              "analysis": "用户要求同时分析存储和虚拟化容量，应生成组合报告",
              "confidence": 0.9,
              "report_sections": ["storage", "virtualization"]
            }
            ```
        - id: user-prompt
          role: user
          text: |
            请分析以下用户需求，判断应该生成哪种类型的容量报告：

            **用户需求：** {{#sys.query#}}

            请严格按照JSON格式输出分析结果。
        selected: false
        title: 问题分类器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 97
      id: question-classifier
      position:
        x: 250
        y: 114.5090375355316
      positionAbsolute:
        x: 250
        y: 114.5090375355316
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - type: text
            value: "{\n  \"report_date\": \"{{#conversation_variables.report_date#}}\",\n  \"system_name\": \"{{#conversation_variables.system_name#}}\",\n  \"query_type\": \"{{#question-classifier.answer#}}\"\n}\n"
          type: json
        desc: 根据查询类型获取对应的容量数据
        headers: Content-Type:application/json
        method: post
        selected: false
        timeout:
          max_connect_timeout: 300
          max_read_timeout: 600
          max_write_timeout: 600
        title: 获取容量数据
        type: http-request
        url: '{{#conversation_variables.api_base_url#}}/api/get_capacity_data'
        variables: []
      height: 54
      id: get-capacity-data
      position:
        x: 550
        y: 114.5090375355316
      positionAbsolute:
        x: 550
        y: 114.5090375355316
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: LLM智能分析容量数据并生成专业报告
        memory:
          query_prompt_template: ''
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params: {}
          mode: chat
          name: deepseek-ai/DeepSeek-V3
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: system-prompt
          role: system
          text: '你是一名专业的IT容量规划专家，擅长分析各类IT资源的容量使用情况并生成专业的容量报告。

            根据用户选择的查询类型，你需要生成对应的容量分析报告：

            - all: 生成包含存储、数据库、容器、虚拟化四个维度的完整报告
            - storage: 只生成存储资源容量报告
            - database: 只生成数据库资源容量报告
            - container: 只生成容器资源容量报告
            - virtualization: 只生成虚拟化资源容量报告
            - 组合类型: 如storage,virtualization，生成指定维度的组合报告

            **重要：报告章节编号规则**
            - 无论查询什么类型，章节编号都从1开始
            - 如果是组合查询，按照以下顺序编号：storage(存储) → database(数据库) → container(容器) → virtualization(虚拟化)
            - 例如：查询storage,virtualization时，存储为"1."，虚拟化为"2."

            请严格按照以下格式生成报告，确保表格格式正确，数据准确：

            # {{#conversation_variables.system_name#}}

            **报告日期：** {{#conversation_variables.report_date#}}
            **查询类型：** {{#question-classifier.answer#}}
            **数据来源：** API自动获取 + LLM智能分析
            **生成时间：** {当前时间}

            ## 报告格式说明：

            ### 报告生成规则：
            1. **章节编号规则**：无论查询什么类型，章节编号都从1开始递增
            2. **组合报告处理**：如果查询类型包含多个维度（用逗号分隔），按以下顺序生成对应章节：
               - storage → database → container → virtualization
            3. **章节编号示例**：
               - 查询"storage"：生成"## 1. 存储资源容量及健康度排查"
               - 查询"storage,virtualization"：生成"## 1. 存储资源容量及健康度排查"和"## 2. 虚拟化资源容量及健康度排查"
               - 查询"database,container"：生成"## 1. 数据库资源容量及健康度排查"和"## 2. 容器资源容量及健康度排查"

            ### 存储资源容量报告格式：
            ## [动态编号]. 存储资源容量及健康度排查
            存储资源池本次排查情况如下：
            | 资源池 | 存储资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|---------------|-------------|------------|-------------------|------------------------|----------|
            [根据存储容量数据填写表格]

            **健康度说明：**
            - 🟢 绿色：正常值 （存储使用率<90%）运行良好。
            - 🟡 黄色：观察值 （存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 🔴 红色：警告值：(存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [基于使用率阈值分析存储池状态]
            **发现问题详情：** [如无问题则说明"今日未发现问题"]
            **应对措施和预案：** [如无问题则说明"不涉及"]

            ### 数据库资源容量报告格式：
            ## [动态编号]. 数据库资源容量及健康度排查
            数据库资源池本次排查情况如下：
            | 资源池 | 数据库资源池名称 | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|-----------------|-------------|------------|-------------------|------------------------|----------|
            [根据数据库容量数据填写表格]

            **健康度说明：**
            - 🟢 绿色：正常值 （数据库使用率<85%）运行良好。
            - 🟡 黄色：观察值 （数据库使用率85%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 🔴 红色：警告值：(数据库使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [基于使用率阈值分析数据库状态]
            **发现问题详情：** [如无问题则说明"今日未发现问题"]
            **应对措施和预案：** [如无问题则说明"不涉及"]

            ### 容器资源容量报告格式：
            ## [动态编号]. 容器资源容量及健康度排查
            容器资源池本次排查情况如下：
            | 资源池 | 容器资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|---------------|---------------|---------------|---------------|-------------------|------------------------|----------|
            [根据容器资源数据填写表格]

            **健康度说明：**
            - 🟢 绿色：正常值 （CPU/内存使用率<80%，存储使用率<90%）运行良好。
            - 🟡 黄色：观察值 （CPU/内存使用率80%~90%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 🔴 红色：警告值：(CPU/内存使用率>90%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [基于使用率阈值分析容器集群状态]
            **发现问题详情：** [如无问题则说明"今日未发现问题"]
            **应对措施和预案：** [如无问题则说明"不涉及"]

            ### 虚拟化资源容量报告格式：
            ## [动态编号]. 虚拟化资源容量及健康度排查
            虚拟化资源池本次排查情况如下：
            | 资源池 | 虚拟化资源池名称 | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|-----------------|---------------|---------------|---------------|-------------------|------------------------|----------|
            [根据虚拟化资源数据填写表格]

            **健康度说明：**
            - 🟢 绿色：正常值 （CPU/内存使用率<75%，存储使用率<90%）运行良好。
            - 🟡 黄色：观察值 （CPU/内存使用率75%~85%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 🔴 红色：警告值：(CPU/内存使用率>85%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [基于使用率阈值分析虚拟化集群状态]
            **发现问题详情：** [如无问题则说明"今日未发现问题"]
            **应对措施和预案：** [如无问题则说明"不涉及"]

            ### 总体风险评估（仅当查询包含多个维度时使用）：
            ## [最后编号]. 总体风险评估和建议
            **整体健康度评估：** [综合分析资源池的健康状况]
            **主要风险点：** [识别需要重点关注的资源池和风险]
            **优化建议：** [提供具体的容量优化和扩容建议]
            **下一步行动计划：** [制定具体的后续行动计划]

            ---

            **重要要求：**
            1. **章节编号规则**：无论查询什么类型，章节编号都从1开始递增
            2. **组合报告处理**：如果查询类型包含多个维度（用逗号分隔），按storage→database→container→virtualization顺序生成
            3. **动态编号示例**：
               - 查询"storage"：生成"## 1. 存储资源容量及健康度排查"
               - 查询"storage,virtualization"：生成"## 1. 存储资源容量及健康度排查"和"## 2. 虚拟化资源容量及健康度排查"
               - 查询"database,container"：生成"## 1. 数据库资源容量及健康度排查"和"## 2. 容器资源容量及健康度排查"
            4. **总体评估**：只有在查询包含多个维度时才添加总体风险评估章节
            5. **数据准确性**：表格数据必须根据提供的容量数据准确填写
            6. **健康度评估**：要根据使用率阈值准确判断
            7. **输出格式**：Markdown格式，确保表格格式正确
            '
        - id: user-prompt
          role: user
          text: '请根据以下容量数据生成专业的容量分析报告：

            **基本信息：**
            - 报告日期：{{#conversation_variables.report_date#}}
            - 系统名称：{{#conversation_variables.system_name#}}
            - 查询类型：{{#question-classifier.answer#}}

            **容量数据：**
            {{#get-capacity-data.body#}}

            **重要说明：**
            根据查询类型"{{#question-classifier.answer#}}"，请生成对应的报告章节：

            - 如果是"all"：生成所有四个维度的完整报告（章节编号1-4，最后加总体评估）
            - 如果是单项查询（如"storage"）：只生成对应的单个章节（编号为1）
            - 如果是组合查询（如"storage,virtualization"）：按顺序生成对应章节（编号从1开始）

            **章节编号规则：**
            - 无论查询什么类型，章节编号都从1开始递增
            - 组合查询按storage→database→container→virtualization顺序编号
            - 如果包含多个维度，最后添加总体风险评估章节

            请严格按照系统提示中的格式要求生成对应的容量报告，包含：
            1. 正确的章节编号（从1开始）
            2. 详细的数据表格
            3. 基于阈值的健康度评估
            4. 问题识别和风险分析
            5. 具体的应对措施和建议
            6. 如果是多维度查询，包含总体风险评估和优化建议

            确保所有数据都准确反映在报告中，表格格式正确，章节编号正确，分析专业深入。
            '
        selected: false
        title: LLM智能分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 97
      id: llm-analysis
      position:
        x: 850
        y: 114.5090375355316
      positionAbsolute:
        x: 850
        y: 114.5090375355316
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - type: text
            value: "{\n  \"report_content\": \"{{#llm-analysis.answer#}}\",\n  \"report_date\": \"{{#conversation_variables.report_date#}}\",\n  \"system_name\": \"{{#conversation_variables.system_name#}}\",\n  \"save_path\": \"D:/work/LLM/reports/\"\n}\n"
          type: json
        desc: 将生成的报告导出为Word文档
        headers: Content-Type:application/json
        method: post
        selected: false
        timeout:
          max_connect_timeout: 300
          max_read_timeout: 600
          max_write_timeout: 600
        title: 导出Word文档
        type: http-request
        url: '{{#conversation_variables.api_base_url#}}/api/export_word'
        variables: []
      height: 54
      id: export-word
      position:
        x: 1150
        y: 114.5090375355316
      positionAbsolute:
        x: 1150
        y: 114.5090375355316
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '## 🎉 智能容量报告生成完成！

          ### 📊 容量数据获取结果
          **查询类型：** {{#question-classifier.answer#}}
          **数据获取状态：** {{#get-capacity-data.body.success#}}
          **数据来源：** API自动获取

          ### 📋 报告内容
          {{#llm-analysis.answer#}}

          ### 📄 Word文档导出结果
          **导出状态：** {{#export-word.body.success#}}
          **文件路径：** {{#export-word.body.file_path#}}
          **文件大小：** {{#export-word.body.file_size#}}
          **文件类型：** {{#export-word.body.file_type#}}

          ### 💡 使用说明
          - Word文档已保存到指定目录
          - 可以直接用Microsoft Word打开查看
          - 支持进一步编辑和格式调整

          ### 🔄 后续操作
          如果您需要：
          - 修改查询范围，请告诉我具体需求
          - 重新生成报告，请说明调整要求
          - 查看其他维度数据，请直接提出

          **感谢使用智能容量报告系统！**'
        desc: 显示最终的报告生成结果
        selected: false
        title: 答案
        type: answer
        variables: []
      height: 107
      id: answer
      position:
        x: 1450
        y: 114.5090375355316
      positionAbsolute:
        x: 1450
        y: 114.5090375355316
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
