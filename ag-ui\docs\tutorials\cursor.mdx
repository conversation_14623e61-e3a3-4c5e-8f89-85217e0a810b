---
title: "Developing with <PERSON>urs<PERSON>"
description: "Use Cursor to build AG-UI implementations faster"
---

This guide will help you set up Cursor to help you build custom Agent User
Interaction Protocol (AG-UI) servers and clients faster. The same principles
apply to other IDE's like Windsurf, VSCode, etc.

## Adding the documentation to Cursor

1. Open up the Cursor settings
2. Go to Features > Docs and click "+ Add new Doc"
3. Paste in the following URL: https://docs.ag-ui.com/llms-full.txt
4. Click "Add"

## Using the documentation

Now you can use the documentation to help you build your AG-UI project. Load the
docs into the current prompt by typing the `@` symbol, selecting "Docs" and then
selecting "Agent User Interaction Protocol" from the list. Happy coding!

## Best practices

When building AG-UI servers with Cursor:

- Break down complex problems into smaller steps
- Have a look at what the agent was doing by checking which files it edited
  (above the chat input)
- Let the agent write unit tests to verify your implementation
- Follow AG-UI protocol specifications carefully
