import os
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>

from .endpoint import add_crewai_flow_fastapi_endpoint
from .examples.agentic_chat import AgenticChat<PERSON>low
from .examples.human_in_the_loop import HumanInTheLoopFlow
from .examples.tool_based_generative_ui import ToolBasedGenerativeUIFlow
from .examples.agentic_generative_ui import AgenticGenerativeUI<PERSON>low
from .examples.shared_state import SharedStateFlow
from .examples.predictive_state_updates import PredictiveStateUpdatesFlow

app = FastAPI(title="CrewAI Dojo Example Server")

add_crewai_flow_fastapi_endpoint(
    app=app,
    flow=AgenticChatFlow(),
    path="/agentic_chat",
)

add_crewai_flow_fastapi_endpoint(
    app=app,
    flow=HumanInTheLoopFlow(),
    path="/human_in_the_loop",
)

add_crewai_flow_fastapi_endpoint(
    app=app,
    flow=ToolBasedGenerativeUIFlow(),
    path="/tool_based_generative_ui",
)

add_crewai_flow_fastapi_endpoint(
    app=app,
    flow=AgenticGenerativeUIFlow(),
    path="/agentic_generative_ui",
)

add_crewai_flow_fastapi_endpoint(
    app=app,
    flow=SharedStateFlow(),
    path="/shared_state",
)

add_crewai_flow_fastapi_endpoint(
    app=app,
    flow=PredictiveStateUpdatesFlow(),
    path="/predictive_state_updates",
)

def main():
    """Run the uvicorn server."""
    port = int(os.getenv("PORT", "8000"))
    uvicorn.run(
        "ag_ui_crewai.dojo:app",
        host="0.0.0.0",
        port=port,
        reload=True
    )
