# Dify Chatflow DSL 示例：订餐助手
# 请注意：这是一个概念性示例，实际导入 Dify 可能需要根据具体版本调整字段。
# Dify DSL 是由 Dify.AI 定义的 AI 应用工程文件标准 [[1]]。

version: '1.0'
type: chatflow # 指定为对话流类型 [[10]]
name: "订餐助手"
description: "一个支持多轮对话的披萨订餐助手"
model:
  provider: openai
  name: gpt-3.5-turbo
  parameters:
    temperature: 0.7
    max_tokens: 500

# 定义流程中使用的变量
variables:
  - name: user_name
    type: string
    description: "用户的姓名"
  - name: pizza_size
    type: string
    description: "披萨尺寸 (小/中/大)"
  - name: toppings
    type: array
    items:
      type: string
    description: "用户选择的配料列表"
  - name: order_confirmed
    type: boolean
    default: false
    description: "订单是否已确认"

# 定义对话流程的节点
nodes:
  # 起始节点：问候并询问姓名
  - id: start
    type: llm
    name: "欢迎并询问姓名"
    content: "您好！欢迎使用披萨订餐服务。我是您的助手。请问您贵姓？"
    # 此节点的输出将被存储到变量中，供后续节点使用
    output_mapping:
      text: "{{user_name}}"
    # 指定下一个节点，等待用户输入姓名
    next_node_id: "ask_pizza_size"

  # 询问披萨尺寸
  - id: ask_pizza_size
    type: llm
    name: "询问披萨尺寸"
    # 使用之前存储的变量
    content: "好的，{{user_name}}！请问您想订什么尺寸的披萨呢？（小/中/大）"
    output_mapping:
      text: "{{pizza_size}}"
    next_node_id: "validate_size"

  # 验证尺寸输入
  - id: validate_size
    type: condition
    name: "验证披萨尺寸"
    # 检查用户输入是否为有效尺寸
    conditions:
      - expression: "{{pizza_size}} in ['小', '中', '大']"
        next_node_id: "ask_toppings" # 有效则继续
      - expression: "True" # 默认情况，即输入无效
        next_node_id: "ask_pizza_size" # 无效则跳回重新询问

  # 询问配料
  - id: ask_toppings
    type: llm
    name: "询问配料"
    content: "明白了，您要一个 {{pizza_size}} 号披萨。那么，请问需要加什么配料呢？（例如：芝士、火腿、蘑菇，可以多选，输入'完成'结束选择）"
    # 这里简化处理，假设用户一次性输入所有配料，用逗号分隔
    output_mapping:
      text: "{{toppings}}" # 实际应用中可能需要更复杂的解析或循环节点
    next_node_id: "confirm_order"

  # 确认订单
  - id: confirm_order
    type: llm
    name: "确认订单"
    content: |
      好的，{{user_name}}！请确认您的订单：
      - 尺寸: {{pizza_size}}
      - 配料: {{toppings | join(', ')}}
      请问确认无误吗？（请回答“确认”或“修改”）
    output_mapping:
      text: "{{user_input_for_confirmation}}" # 临时存储用户确认输入
    next_node_id: "handle_confirmation"

  # 处理确认
  - id: handle_confirmation
    type: condition
    name: "处理用户确认"
    conditions:
      - expression: "'确认' in '{{user_input_for_confirmation}}'"
        next_node_id: "finalize_order"
      - expression: "'修改' in '{{user_input_for_confirmation}}'"
        next_node_id: "ask_pizza_size" # 跳回修改，简化处理，实际可设计更精细的修改流程
      - expression: "True"
        next_node_id: "confirm_order" # 其他输入则重新询问确认

  # 最终确认并结束
  - id: finalize_order
    type: llm
    name: "订单完成"
    content: "太好了！{{user_name}}，您的订单【{{pizza_size}}号披萨，配料：{{toppings | join(', ')}}】已成功提交！感谢您的惠顾，祝您用餐愉快！"
    # 此节点后无 next_node_id，流程结束

# 定义流程入口
start_node_id: "start"