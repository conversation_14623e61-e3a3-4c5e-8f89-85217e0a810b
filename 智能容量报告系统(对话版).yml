app:
  description: 通过对话方式生成智能容量报告，支持多轮交互和组合查询
  icon: 💬
  icon_background: '#2563EB'
  mode: advanced-chat
  name: 智能容量报告系统(对话版)
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.22@588c9f60178d37985626f1118347eb92bedcd8cb27d25bafc4ac929027ec51dc
kind: app
version: 0.3.1
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: "欢迎使用智能容量报告系统(对话版)！\n系统功能：智能对话交互：通过自然语言对话生成容量报告，支持多种查询模式：\n\
      \ • 完整容量报告（存储+数据库+容器+虚拟化） \n • 单项查询（存储、数据库、容器、虚拟化）\n • 组合查询（如：存储+虚拟化、数据库+容器等）\n\
      \  LLM智能分析和风险评估 按规定格式生成专业报告 自动导出Word文档格式\n对话优势： • 多轮交互：可以追问和补充需求 \n • 实时反馈：即时查看分析结果\n\
      \ • 灵活调整：可以修改查询范围\n • 历史记录：保留对话上下文\n 使用方式： 直接告诉我您的需求，例如： • \"我需要生成存储和虚拟化的容量报告\"\
      \ • \"帮我分析一下数据库的容量情况\" • \"生成完整的IT基础设施容量报告\"\n预计处理时间：2-4分钟  文档保存位置：./reports/\
      \ 目录"
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 我需要生成一份完整的IT基础设施容量报告
    - 帮我检查存储和虚拟化的容量状况
    - 生成数据库容量专项分析报告
    - 我想了解容器集群的资源使用情况
    - 请分析存储、数据库、虚拟化三个方面的容量
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: http-request
        targetType: answer
      id: export-word-answer
      selected: false
      source: export-word
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: parameter-extractor
      id: question-classifier-source-1752462803065-target
      selected: false
      source: question-classifier
      sourceHandle: source
      target: '1752462803065'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: parameter-extractor
      id: llm-analysis-source-1752462814912-target
      selected: false
      source: llm-analysis
      sourceHandle: source
      target: '1752462814912'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: parameter-extractor
        targetType: http-request
      id: 1752462814912-source-export-word-target
      selected: false
      source: '1752462814912'
      sourceHandle: source
      target: export-word
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: parameter-extractor
        targetType: tool
      id: 1752462803065-source-1752462905130-target
      selected: false
      source: '1752462803065'
      sourceHandle: source
      target: '1752462905130'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: tool
        targetType: http-request
      id: 1752462905130-source-get-capacity-data-target
      selected: false
      source: '1752462905130'
      sourceHandle: source
      target: get-capacity-data
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: 1752471935532-source-1752471174484-target
      selected: false
      source: '1752471935532'
      sourceHandle: source
      target: '1752471174484'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: llm
      id: 1752471167268-1-question-classifier-target
      selected: false
      source: '1752471167268'
      sourceHandle: '1'
      target: question-classifier
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: http-request
        targetType: code
      id: get-capacity-data-source-1752557862255-target
      selected: false
      source: get-capacity-data
      sourceHandle: source
      target: '1752557862255'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: question-classifier
      id: start-source-1752471167268-target
      selected: false
      source: start
      sourceHandle: source
      target: '1752471167268'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: 1752557862255-source-llm-analysis-target
      selected: false
      source: '1752557862255'
      sourceHandle: source
      target: llm-analysis
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: llm
      id: 1752471167268-1752738102568-1752471935532-target
      selected: false
      source: '1752471167268'
      sourceHandle: '1752738102568'
      target: '1752471935532'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: 对话开始节点
        selected: false
        title: 开始
        type: start
        variables: []
      height: 81
      id: start
      position:
        x: -50.767987465007366
        y: -106.38241326707764
      positionAbsolute:
        x: -50.767987465007366
        y: -106.38241326707764
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: 分析用户需求，自动判断应该生成哪种类型的容量报告
        model:
          completion_params: {}
          mode: chat
          name: deepseek-ai/DeepSeek-V3
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: system-prompt
          role: system
          text: "你是一个专业的IT容量报告需求分析专家，负责分析用户的自然语言需求，并判断应该生成哪种类型的容量报告。\n\n## 支持的报告类型：\n\
            1. **all** - 完整容量报告（包含存储、数据库、容器、虚拟化所有维度）\n2. **storage** - 存储容量专项报告\n\
            3. **database** - 数据库容量专项报告\n4. **container** - 容器容量专项报告\n5. **virtualization**\
            \ - 虚拟化容量专项报告\n6. **组合类型** - 多个维度的组合报告，用逗号分隔，如：storage,virtualization\n\
            \n## 分析规则：\n- 如果用户提到\"完整\"、\"全面\"、\"所有\"、\"整体\"等词汇，选择 **all**\n- 如果用户明确提到\"\
            存储\"、\"磁盘\"、\"硬盘\"等，包含 **storage**\n- 如果用户明确提到\"数据库\"、\"DB\"、\"MySQL\"\
            、\"Oracle\"等，包含 **database**\n- 如果用户明确提到\"容器\"、\"Docker\"、\"Kubernetes\"\
            、\"K8s\"等，包含 **container**\n- 如果用户明确提到\"虚拟化\"、\"虚拟机\"、\"VM\"、\"ESX\"、\"\
            vSphere\"等，包含 **virtualization**\n- 如果用户提到多个维度，用逗号分隔组合，如：storage,virtualization\n\
            - 如果需求不明确，默认选择 **all**\n\n## 输出格式：\n你必须严格按照以下JSON格式输出，不要包含任何其他内容：\n```json\n\
            {\n  \"query_type\": \"报告类型\",\n  \"analysis\": \"需求分析说明\",\n  \"confidence\"\
            : \"置信度(0-1)\",\n  \"report_sections\": [\"章节列表\"]\n}\n```\n\n## 示例：\n\
            示例1 - 单项查询：\n用户需求：\"我需要检查存储系统的容量使用情况\"\n输出：\n```json\n{\n  \"query_type\"\
            : \"storage\",\n  \"analysis\": \"用户明确要求检查存储系统容量，应生成存储专项报告\",\n  \"confidence\"\
            : 0.95,\n  \"report_sections\": [\"storage\"]\n}\n```\n\n示例2 - 组合查询：\n\
            用户需求：\"我需要同时分析存储和虚拟化的容量状况\"\n输出：\n```json\n{\n  \"query_type\": \"storage,virtualization\"\
            ,\n  \"analysis\": \"用户要求同时分析存储和虚拟化容量，应生成组合报告\",\n  \"confidence\": 0.9,\n\
            \  \"report_sections\": [\"storage\", \"virtualization\"]\n}\n```\n"
        - id: user-prompt
          role: user
          text: '请分析以下用户需求，判断应该生成哪种类型的容量报告：


            **用户需求：** {{#sys.query#}}


            请严格按照JSON格式输出分析结果。

            '
        selected: false
        title: 问题分类器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 133
      id: question-classifier
      position:
        x: 228.66331343342523
        y: -128.79562050581302
      positionAbsolute:
        x: 228.66331343342523
        y: -128.79562050581302
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - type: text
            value: "{\n  \"report_date\": \"{{#1752462905130.text#}}\",\n  \"system_name\"\
              : \"{{#1752462803065.typt#}}\",\n  \"query_type\": \"{{#question-classifier.answer#}}\"\
              \n}\n"
          type: json
        desc: 根据查询类型获取对应的容量数据
        headers: Content-Type:application/json;charset=utf-8
        method: post
        params: ''
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 100
        selected: false
        ssl_verify: true
        timeout:
          max_connect_timeout: 300
          max_read_timeout: 600
          max_write_timeout: 600
        title: 获取容量数据
        type: http-request
        url: http://172.30.224.1:5000/api/get_capacity_data
        variables: []
      height: 167
      id: get-capacity-data
      position:
        x: 304.91283602435215
        y: 509.5859022226267
      positionAbsolute:
        x: 304.91283602435215
        y: 509.5859022226267
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: LLM智能分析容量数据并生成专业报告
        memory:
          query_prompt_template: ''
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen3-235B-A22B-Instruct-2507
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: system-prompt
          role: system
          text: '你是一名专业的IT容量规划专家，擅长分析各类IT资源的容量使用情况并生成专业的容量报告。

            根据用户选择的查询类型，你需要生成对应的容量分析报告：

            - all: 生成包含存储、数据库、容器、虚拟化四个维度的完整报告 - storage: 只生成存储资源容量报告 - database: 只生成数据库资源容量报告
            - container: 只生成容器资源容量报告 - virtualization: 只生成虚拟化资源容量报告 - 组合类型: 如storage,virtualization，生成指定维度的组合报告

            **重要：报告章节编号规则** - 无论查询什么类型，章节编号都从1开始 - 如果是组合查询，按照以下顺序编号：storage(存储) →
            database(数据库) → container(容器) → virtualization(虚拟化) - 例如：查询storage,virtualization时，存储为"1."，虚拟化为"2."

            请严格按照以下格式生成报告，确保表格格式正确，数据准确：

            # {{#1752462803065.typt#}}容量报告

            **报告日期：** {{#1752462905130.text#}} **查询类型：** {{#1752462803065.typt#}}
            **数据来源：** API自动获取 + LLM智能分析 **生成时间：** {当前时间}

            ## 报告格式说明：

            ### 报告生成规则： 1. **章节编号规则**：无论查询什么类型，章节编号都从1开始递增 2. **组合报告处理**：如果查询类型包含多个维度（用逗号分隔），按以下顺序生成对应章节：
            - storage → database → container → virtualization 3. **章节编号示例**： - 查询"storage"：生成"##
            1. 存储资源容量及健康度排查" - 查询"storage,virtualization"：生成"## 1. 存储资源容量及健康度排查"和"##
            2. 虚拟化资源容量及健康度排查" - 查询"database,container"：生成"## 1. 数据库资源容量及健康度排查"和"##
            2. 容器资源容量及健康度排查"

            ### 存储资源容量报告格式： ## [动态编号]. 存储资源容量及健康度排查 存储资源池本次排查情况如下： | 资源池 | 存储资源池名称
            | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 | |--------|---------------|-------------|------------|-------------------|------------------------|----------|
            [根据存储容量数据填写表格]

            **健康度说明：** - 🟢 绿色：正常值 （存储使用率<90%）运行良好。 - 🟡 黄色：观察值 （存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 🔴 红色：警告值：(存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [基于使用率阈值分析存储池状态] **发现问题详情：** [如无问题则说明"今日未发现问题"] **应对措施和预案：**
            [如无问题则说明"不涉及"]

            ### 数据库资源容量报告格式： ## [动态编号]. 数据库资源容量及健康度排查 数据库资源池本次排查情况如下： | 资源池 | 数据库资源池名称
            | 总容量（GB） | 使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 | |--------|-----------------|-------------|------------|-------------------|------------------------|----------|
            [根据数据库容量数据填写表格]

            **健康度说明：** - 🟢 绿色：正常值 （数据库使用率<85%）运行良好。 - 🟡 黄色：观察值 （数据库使用率85%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 🔴 红色：警告值：(数据库使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [基于使用率阈值分析数据库状态] **发现问题详情：** [如无问题则说明"今日未发现问题"] **应对措施和预案：**
            [如无问题则说明"不涉及"]

            ### 容器资源容量报告格式： ## [动态编号]. 容器资源容量及健康度排查 容器资源池本次排查情况如下： | 资源池 | 容器资源池名称
            | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|---------------|---------------|---------------|---------------|-------------------|------------------------|----------|
            [根据容器资源数据填写表格]

            **健康度说明：** - 🟢 绿色：正常值 （CPU/内存使用率<80%，存储使用率<90%）运行良好。 - 🟡 黄色：观察值 （CPU/内存使用率80%~90%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 🔴 红色：警告值：(CPU/内存使用率>90%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [基于使用率阈值分析容器集群状态] **发现问题详情：** [如无问题则说明"今日未发现问题"] **应对措施和预案：**
            [如无问题则说明"不涉及"]

            ### 虚拟化资源容量报告格式： ## [动态编号]. 虚拟化资源容量及健康度排查 虚拟化资源池本次排查情况如下： | 资源池 | 虚拟化资源池名称
            | CPU使用率（%） | 内存使用率（%） | 存储使用率（%） | 对比前日值变化情况 | 是否存在异常波动/资源不足 | 对应措施 |
            |--------|-----------------|---------------|---------------|---------------|-------------------|------------------------|----------|
            [根据虚拟化资源数据填写表格]

            **健康度说明：** - 🟢 绿色：正常值 （CPU/内存使用率<75%，存储使用率<90%）运行良好。 - 🟡 黄色：观察值 （CPU/内存使用率75%~85%，存储使用率90%~95%）需要关注，向调度部报备，结合资源情况制定调整方案并实施。
            - 🔴 红色：警告值：(CPU/内存使用率>85%，存储使用率>95%) 资源不足，向安监部报备隐患，制定应急处置方案，应急处置。

            **今日状态：** [基于使用率阈值分析虚拟化集群状态] **发现问题详情：** [如无问题则说明"今日未发现问题"] **应对措施和预案：**
            [如无问题则说明"不涉及"]

            ### 总体风险评估（仅当查询包含多个维度时使用）： ## [最后编号]. 总体风险评估和建议 **整体健康度评估：** [综合分析资源池的健康状况]
            **主要风险点：** [识别需要重点关注的资源池和风险] **优化建议：** [提供具体的容量优化和扩容建议] **下一步行动计划：** [制定具体的后续行动计划]

            ---

            **重要要求：** 1. **章节编号规则**：无论查询什么类型，章节编号都从1开始递增 2. **组合报告处理**：如果查询类型包含多个维度（用逗号分隔），按storage→database→container→virtualization顺序生成
            3. **动态编号示例**： - 查询"storage"：生成"## 1. 存储资源容量及健康度排查" - 查询"storage,virtualization"：生成"##
            1. 存储资源容量及健康度排查"和"## 2. 虚拟化资源容量及健康度排查" - 查询"database,container"：生成"##
            1. 数据库资源容量及健康度排查"和"## 2. 容器资源容量及健康度排查" 4. **总体评估**：只有在查询包含多个维度时才添加总体风险评估章节
            5. **数据准确性**：表格数据必须根据提供的容量数据准确填写 6. **健康度评估**：要根据使用率阈值准确判断 7. **输出格式**：Markdown格式，确保表格格式正确 '
        - id: user-prompt
          role: user
          text: '请根据以下容量数据生成专业的容量分析报告：

            **基本信息：** - 报告日期：{{#1752462905130.text#}} - 系统名称：{{#1752462803065.typt#}}容量报告
            - 查询类型：{{#1752462803065.typt#}}

            **容量数据：** {{#1752557862255.result#}}

            **重要说明：** 根据查询类型"{{#1752462803065.typt#}}"，请生成对应的报告章节：

            - 如果是"all"：生成所有四个维度的完整报告（章节编号1-4，最后加总体评估） - 如果是单项查询（如"storage"）：只生成对应的单个章节（编号为1）
            - 如果是组合查询（如"storage,virtualization"）：按顺序生成对应章节（编号从1开始）

            **章节编号规则：** - 无论查询什么类型，章节编号都从1开始递增 - 组合查询按storage→database→container→virtualization顺序编号
            - 如果包含多个维度，最后添加总体风险评估章节

            请严格按照系统提示中的格式要求生成对应的容量报告，包含： 1. 正确的章节编号（从1开始） 2. 详细的数据表格 3. 基于阈值的健康度评估
            4. 问题识别和风险分析 5. 具体的应对措施和建议 6. 如果是多维度查询，包含总体风险评估和优化建议

            确保所有数据都准确反映在报告中，表格格式正确，章节编号正确，分析专业深入。 '
        selected: true
        title: LLM智能分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 117
      id: llm-analysis
      position:
        x: 641.638686960143
        y: 86.62798945056036
      positionAbsolute:
        x: 641.638686960143
        y: 86.62798945056036
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - type: text
            value: "{\n  \"report_content\": \"{{#1752462814912.text#}}\",\n  \"report_date\"\
              : \"{{#1752462905130.text#}}\",\n  \"system_name\": \"超级无敌小猫咪\",\n \
              \ \"save_path\": \"D:/work/LLM/reports/\"\n}\n"
          type: json
        desc: 将生成的报告导出为Word文档
        headers: Content-Type:application/json
        method: post
        params: ''
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 100
        selected: false
        ssl_verify: true
        timeout:
          max_connect_timeout: 300
          max_read_timeout: 600
          max_write_timeout: 600
        title: 导出Word文档
        type: http-request
        url: http://172.30.224.1:5000/api/export_word
        variables: []
      height: 167
      id: export-word
      position:
        x: 641.638686960143
        y: 509.5859022226267
      positionAbsolute:
        x: 641.638686960143
        y: 509.5859022226267
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '##  智能容量报告生成完成！

          ###  容量数据获取结果 **查询类型：** {{#1752462803065.typt#}}  **数据来源：** API自动获取

          ###  报告内容 {{#1752462814912.text#}}

          ###  Word文档导出结果 **导出状态：** 导出成功 **文件路径：** D:/work/LLM/reports/ **

          ###  使用说明 - Word文档已保存到指定目录 - 可以直接用Microsoft Word打开查看 - 支持进一步编辑和格式调整

          ###  后续操作 如果您需要： - 修改查询范围，请告诉我具体需求 - 重新生成报告，请说明调整要求 - 查看其他维度数据，请直接提出

          **感谢使用智能容量报告系统！**'
        desc: 显示最终的报告生成结果
        selected: false
        title: 答案
        type: answer
        variables: []
      height: 359
      id: answer
      position:
        x: 949.9607089779597
        y: 125.70878036894726
      positionAbsolute:
        x: 949.9607089779597
        y: 125.70878036894726
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: deepseek-ai/DeepSeek-V3
          provider: langgenius/siliconflow/siliconflow
        parameters:
        - description: 报告类型
          name: typt
          required: false
          type: string
        query:
        - question-classifier
        - text
        reasoning_mode: prompt
        selected: false
        title: 提取问题类型
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1752462803065'
      position:
        x: 356.397178671402
        y: 102.56667629718945
      positionAbsolute:
        x: 356.397178671402
        y: 102.56667629718945
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        instruction: qurey的值全部提取出来
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: Qwen/Qwen3-235B-A22B-Instruct-2507
          provider: langgenius/siliconflow/siliconflow
        parameters:
        - description: 报告内容
          name: text
          required: false
          type: string
        query:
        - llm-analysis
        - text
        reasoning_mode: prompt
        selected: false
        title: 获取报告内容
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1752462814912'
      position:
        x: 641.638686960143
        y: 294.6073993358423
      positionAbsolute:
        x: 641.638686960143
        y: 294.6073993358423
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: '%Y-%m-%d %H:%M:%S'
          form: form
          human_description:
            en_US: Time format in strftime standard.
            ja_JP: Time format in strftime standard.
            pt_BR: Time format in strftime standard.
            zh_Hans: strftime 标准的时间格式。
          label:
            en_US: Format
            ja_JP: Format
            pt_BR: Format
            zh_Hans: 格式
          llm_description: null
          max: null
          min: null
          name: format
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: UTC
          form: form
          human_description:
            en_US: Timezone
            ja_JP: Timezone
            pt_BR: Timezone
            zh_Hans: 时区
          label:
            en_US: Timezone
            ja_JP: Timezone
            pt_BR: Timezone
            zh_Hans: 时区
          llm_description: null
          max: null
          min: null
          name: timezone
          options:
          - label:
              en_US: UTC
              ja_JP: UTC
              pt_BR: UTC
              zh_Hans: UTC
            value: UTC
          - label:
              en_US: America/New_York
              ja_JP: America/New_York
              pt_BR: America/New_York
              zh_Hans: 美洲/纽约
            value: America/New_York
          - label:
              en_US: America/Los_Angeles
              ja_JP: America/Los_Angeles
              pt_BR: America/Los_Angeles
              zh_Hans: 美洲/洛杉矶
            value: America/Los_Angeles
          - label:
              en_US: America/Chicago
              ja_JP: America/Chicago
              pt_BR: America/Chicago
              zh_Hans: 美洲/芝加哥
            value: America/Chicago
          - label:
              en_US: America/Sao_Paulo
              ja_JP: America/Sao_Paulo
              pt_BR: América/São Paulo
              zh_Hans: 美洲/圣保罗
            value: America/Sao_Paulo
          - label:
              en_US: Asia/Shanghai
              ja_JP: Asia/Shanghai
              pt_BR: Asia/Shanghai
              zh_Hans: 亚洲/上海
            value: Asia/Shanghai
          - label:
              en_US: Asia/Ho_Chi_Minh
              ja_JP: Asia/Ho_Chi_Minh
              pt_BR: Ásia/Ho Chi Minh
              zh_Hans: 亚洲/胡志明市
            value: Asia/Ho_Chi_Minh
          - label:
              en_US: Asia/Tokyo
              ja_JP: Asia/Tokyo
              pt_BR: Asia/Tokyo
              zh_Hans: 亚洲/东京
            value: Asia/Tokyo
          - label:
              en_US: Asia/Dubai
              ja_JP: Asia/Dubai
              pt_BR: Asia/Dubai
              zh_Hans: 亚洲/迪拜
            value: Asia/Dubai
          - label:
              en_US: Asia/Kolkata
              ja_JP: Asia/Kolkata
              pt_BR: Asia/Kolkata
              zh_Hans: 亚洲/加尔各答
            value: Asia/Kolkata
          - label:
              en_US: Asia/Seoul
              ja_JP: Asia/Seoul
              pt_BR: Asia/Seoul
              zh_Hans: 亚洲/首尔
            value: Asia/Seoul
          - label:
              en_US: Asia/Singapore
              ja_JP: Asia/Singapore
              pt_BR: Asia/Singapore
              zh_Hans: 亚洲/新加坡
            value: Asia/Singapore
          - label:
              en_US: Europe/London
              ja_JP: Europe/London
              pt_BR: Europe/London
              zh_Hans: 欧洲/伦敦
            value: Europe/London
          - label:
              en_US: Europe/Berlin
              ja_JP: Europe/Berlin
              pt_BR: Europe/Berlin
              zh_Hans: 欧洲/柏林
            value: Europe/Berlin
          - label:
              en_US: Europe/Moscow
              ja_JP: Europe/Moscow
              pt_BR: Europe/Moscow
              zh_Hans: 欧洲/莫斯科
            value: Europe/Moscow
          - label:
              en_US: Australia/Sydney
              ja_JP: Australia/Sydney
              pt_BR: Australia/Sydney
              zh_Hans: 澳大利亚/悉尼
            value: Australia/Sydney
          - label:
              en_US: Pacific/Auckland
              ja_JP: Pacific/Auckland
              pt_BR: Pacific/Auckland
              zh_Hans: 太平洋/奥克兰
            value: Pacific/Auckland
          - label:
              en_US: Africa/Cairo
              ja_JP: Africa/Cairo
              pt_BR: Africa/Cairo
              zh_Hans: 非洲/开罗
            value: Africa/Cairo
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: select
        params:
          format: ''
          timezone: ''
        provider_id: time
        provider_name: time
        provider_type: builtin
        selected: false
        title: 获取当前时间
        tool_configurations:
          format:
            type: constant
            value: '%Y-%m-%d'
          timezone:
            type: constant
            value: UTC
        tool_description: 一个用于获取当前时间的工具。
        tool_label: 获取当前时间
        tool_name: current_time
        tool_parameters: {}
        type: tool
        version: '2'
      height: 115
      id: '1752462905130'
      position:
        x: 323.5699621722156
        y: 279.9760009328877
      positionAbsolute:
        x: 323.5699621722156
        y: 279.9760009328877
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        classes:
        - id: '1'
          name: 用户需要生成容量报告
        - id: '1752738102568'
          name: 其他内容
        desc: ''
        instructions: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: deepseek-ai/DeepSeek-V3
          provider: langgenius/siliconflow/siliconflow
        query_variable_selector:
        - start
        - sys.query
        selected: false
        title: 问题分类器
        topics: []
        type: question-classifier
        vision:
          enabled: false
      height: 171
      id: '1752471167268'
      position:
        x: -43.17178362632649
        y: 86.62798945056036
      positionAbsolute:
        x: -43.17178362632649
        y: 86.62798945056036
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1752471935532.text#}}'
        desc: ''
        selected: false
        title: 直接回复 2
        type: answer
        variables: []
      height: 104
      id: '1752471174484'
      position:
        x: -15.696963061543158
        y: 567.7389904544411
      positionAbsolute:
        x: -15.696963061543158
        y: 567.7389904544411
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: Qwen/Qwen3-235B-A22B-Instruct-2507
          provider: langgenius/siliconflow/siliconflow
        prompt_template:
        - id: 62a74575-fa00-4b28-b415-6d6e6fd4acd1
          role: system
          text: 请根据{{#sys.query#}}回答
        selected: false
        title: LLM 3
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1752471935532'
      position:
        x: -10.714142916997787
        y: 394.221097171468
      positionAbsolute:
        x: -10.714142916997787
        y: 394.221097171468
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "\ndef main(arg1) -> dict:\n    return {\n        \"result\": arg1\n\
          \    }\n"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 代码执行
        type: code
        variables:
        - value_selector:
          - get-capacity-data
          - body
          value_type: string
          variable: arg1
      height: 53
      id: '1752557862255'
      position:
        x: 649.9895661544267
        y: -96.29310664560322
      positionAbsolute:
        x: 649.9895661544267
        y: -96.29310664560322
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 394.2794671710342
      y: 160.9630553583109
      zoom: 0.5987393523094644
