#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
容量数据收集定时任务管理脚本
支持Windows任务计划程序和Linux cron
"""

import os
import sys
import time
import schedule
import logging
import subprocess
from datetime import datetime, timedelta
from capacity_collector_config import SCHEDULE_CONFIG, LOGGING_CONFIG, get_config_info

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG['level']),
    format=LOGGING_CONFIG['format'],
    handlers=[
        logging.FileHandler('schedule_capacity_collector.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CapacityScheduler:
    """容量数据收集定时任务调度器"""
    
    def __init__(self):
        self.script_path = os.path.join(os.path.dirname(__file__), 'daily_capacity_collector.py')
        self.is_running = False
        
    def run_collection_task(self):
        """执行容量数据收集任务"""
        if self.is_running:
            logger.warning("容量收集任务正在运行中，跳过本次执行")
            return
        
        try:
            self.is_running = True
            logger.info("开始执行定时容量数据收集任务")
            
            # 执行收集脚本
            result = subprocess.run([
                sys.executable, self.script_path
            ], capture_output=True, text=True, timeout=1800)  # 30分钟超时
            
            if result.returncode == 0:
                logger.info("容量数据收集任务执行成功")
                logger.info(f"输出: {result.stdout}")
            else:
                logger.error("容量数据收集任务执行失败")
                logger.error(f"错误输出: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            logger.error("容量数据收集任务执行超时")
        except Exception as e:
            logger.error(f"执行容量数据收集任务时出错: {e}")
        finally:
            self.is_running = False
    
    def setup_schedule(self):
        """设置定时任务"""
        daily_time = SCHEDULE_CONFIG['daily_time']
        time_str = daily_time.strftime('%H:%M')
        
        logger.info(f"设置每日定时任务: {time_str}")
        
        # 设置每日执行时间
        job = schedule.every().day.at(time_str).do(self.run_collection_task)
        
        # 如果不在周末执行
        if not SCHEDULE_CONFIG['enable_weekend']:
            # 取消周末的任务
            schedule.every().saturday.at(time_str).do(self.run_collection_task).cancel()
            schedule.every().sunday.at(time_str).do(self.run_collection_task).cancel()
        
        logger.info("定时任务设置完成")
        return job
    
    def run_scheduler(self):
        """运行定时任务调度器"""
        logger.info("容量数据收集定时调度器启动")
        logger.info(f"配置信息: {get_config_info()}")
        
        # 设置定时任务
        self.setup_schedule()
        
        # 显示下次执行时间
        next_run = schedule.next_run()
        if next_run:
            logger.info(f"下次执行时间: {next_run}")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            logger.info("用户中断，停止定时调度器")
        except Exception as e:
            logger.error(f"定时调度器运行异常: {e}")
    
    def run_once(self):
        """立即执行一次收集任务"""
        logger.info("立即执行容量数据收集任务")
        self.run_collection_task()

def create_windows_task():
    """创建Windows定时任务"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        python_exe = sys.executable
        script_path = os.path.join(script_dir, 'daily_capacity_collector.py')
        
        daily_time = SCHEDULE_CONFIG['daily_time']
        time_str = daily_time.strftime('%H:%M')
        
        # 创建Windows任务计划
        cmd = f'''schtasks /create /tn "CapacityDataCollection" /tr "{python_exe} {script_path}" /sc daily /st {time_str} /f'''
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("Windows定时任务创建成功")
            logger.info(f"任务名称: CapacityDataCollection")
            logger.info(f"执行时间: 每日 {time_str}")
            return True
        else:
            logger.error(f"Windows定时任务创建失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"创建Windows定时任务时出错: {e}")
        return False

def create_linux_cron():
    """创建Linux cron任务"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        python_exe = sys.executable
        script_path = os.path.join(script_dir, 'daily_capacity_collector.py')
        log_path = os.path.join(script_dir, 'capacity_collector_cron.log')
        
        daily_time = SCHEDULE_CONFIG['daily_time']
        hour = daily_time.hour
        minute = daily_time.minute
        
        # 创建cron表达式
        cron_line = f"{minute} {hour} * * * {python_exe} {script_path} >> {log_path} 2>&1"
        
        # 添加到crontab
        result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
        current_cron = result.stdout if result.returncode == 0 else ""
        
        # 检查是否已存在
        if 'daily_capacity_collector.py' not in current_cron:
            new_cron = current_cron + f"\n# 容量数据收集任务\n{cron_line}\n"
            
            # 写入新的crontab
            process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE, text=True)
            process.communicate(input=new_cron)
            
            if process.returncode == 0:
                logger.info("Linux cron任务创建成功")
                logger.info(f"执行时间: 每日 {hour:02d}:{minute:02d}")
                return True
            else:
                logger.error("Linux cron任务创建失败")
                return False
        else:
            logger.info("Linux cron任务已存在")
            return True
            
    except Exception as e:
        logger.error(f"创建Linux cron任务时出错: {e}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='容量数据收集定时任务管理')
    parser.add_argument('--mode', choices=['run', 'schedule', 'install-windows', 'install-linux'], 
                       default='schedule', help='运行模式')
    parser.add_argument('--once', action='store_true', help='立即执行一次')
    
    args = parser.parse_args()
    
    scheduler = CapacityScheduler()
    
    if args.once:
        scheduler.run_once()
    elif args.mode == 'run':
        scheduler.run_once()
    elif args.mode == 'schedule':
        scheduler.run_scheduler()
    elif args.mode == 'install-windows':
        create_windows_task()
    elif args.mode == 'install-linux':
        create_linux_cron()

if __name__ == '__main__':
    main()
