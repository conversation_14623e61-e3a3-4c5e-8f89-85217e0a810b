{"name": "ag-ui", "author": "<PERSON> <<EMAIL>>", "private": true, "scripts": {"build": "turbo run build", "clean": "rm -rf dist .turbo node_modules && pnpm -r clean", "build:clean": "rm -rf dist .turbo node_modules && pnpm -r clean && pnpm install && turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md,mdx}\"", "check-types": "turbo run check-types", "test": "turbo run test", "bump": "pnpm --filter './packages/*' exec -- pnpm version", "bump:alpha": "pnpm --filter './packages/*' exec -- pnpm version --preid alpha", "publish": "pnpm -r clean && pnpm install && turbo run build && pnpm publish -r --filter='./packages/*'", "publish:alpha": "pnpm -r clean && pnpm install && turbo run build && pnpm publish -r --no-git-checks --filter='./packages/*' --tag alpha"}, "devDependencies": {"prettier": "^3.5.3", "turbo": "^2.4.4", "typescript": "5.8.2"}, "packageManager": "pnpm@10.13.1", "engines": {"node": ">=18"}, "version": "0.0.1"}