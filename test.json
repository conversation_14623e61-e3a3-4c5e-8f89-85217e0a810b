{"nodes": [{"data": {"form": {"enablePrologue": true, "inputs": {}, "mode": "conversational", "prologue": "你好！ 我是你的助理，有什么可以帮到你的吗？"}, "label": "<PERSON><PERSON>", "name": "begin"}, "id": "begin", "measured": {"height": 48, "width": 200}, "position": {"x": -18, "y": 20}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode", "dragging": false}, {"data": {"form": {"frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "items": [{"description": "完整容量报告", "examples": [{"value": "生成完整的IT基础设施容量报告"}], "name": "all", "uuid": "11111111-1111-1111-1111-111111111111"}, {"description": "存储容量专项报告", "examples": [{"value": "检查存储容量"}], "name": "storage", "uuid": "*************-2222-2222-************"}, {"description": "数据库容量专项报告", "examples": [{"value": "分析数据库容量"}], "name": "database", "uuid": "*************-3333-3333-************"}, {"description": "容器容量专项报告", "examples": [{"value": "容器资源使用情况"}], "name": "container", "uuid": "*************-4444-4444-************"}, {"description": "虚拟化容量专项报告", "examples": [{"value": "虚拟化容量"}], "name": "virtualization", "uuid": "*************-5555-5555-************"}, {"name": "others", "description": "不符合容量报告的问题", "uuid": "d69b6476-1bcd-473d-ba39-4d4f2fb50655", "examples": [{"value": "今天星期几"}, {"value": "帮我生成旅游计划"}, {"value": "帮我润色这段话"}]}], "llm_id": "Qwen/Qwen3-235B-A22B@SILICONFLOW", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 20, "outputs": {"category_name": {"type": "string"}}, "parameter": "Precise", "presencePenaltyEnabled": false, "presence_penalty": 0.4, "query": "sys.query", "temperature": 0.1, "temperatureEnabled": false, "topPEnabled": false, "top_p": 0.3}, "label": "Categorize", "name": "问题分类_容量类型"}, "dragging": false, "id": "Categorize:CapacityType", "measured": {"height": 264, "width": 200}, "position": {"x": 569.1293636405858, "y": -211.12839815786654}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "categorizeNode"}, {"data": {"form": {"clean_html": false, "headers": "{\n  \"Accept\": \"application/json\",\n  \"Content-Type\": \"application/json\"\n}", "method": "POST", "outputs": {"result": {"type": "string", "value": ""}}, "proxy": "", "timeout": 60, "url": "http://************:5000/api/get_capacity_data", "variables": []}, "label": "Invoke", "name": "HTTP 请求_获取容量数据"}, "dragging": false, "id": "Invoke:GetCapacityData", "measured": {"height": 56, "width": 200}, "position": {"x": 927.9099013710895, "y": -91.69282404214505}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "ragNode"}, {"data": {"form": {"delay_after_error": 1, "description": "通过对话方式生成智能容量报告，支持多轮交互与组合查询；保留HTTP数据获取与Word导出步骤。", "exception_default_value": "", "exception_goto": [], "exception_method": "", "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "Qwen/Qwen3-235B-A22B@SILICONFLOW", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 1, "max_tokens": 512, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "{sys.query}", "role": "user"}], "sys_prompt": "你是一名专业的IT容量规划专家，擅长分析存储、数据库、容器、虚拟化等资源的容量使用情况并生成专业报告。\n\n要求：\n- 章节编号从1开始；组合按 storage -> database -> container -> virtualization 顺序；多维度最后增加总体风险评估。\n- 基于阈值进行健康度评估：存储<90/90~95/>95；数据库CPU/内存<70/70~85/>85；容器CPU/内存<80/80~90/>90；虚拟化CPU/内存<75/75~85/>85，存储<90/90~95/>95。\n- 输出 Markdown 表格，结论清晰专业。", "temperature": 0.1, "temperatureEnabled": false, "tools": [], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": ""}, "label": "Agent", "name": "Agent_容量报告分析"}, "dragging": false, "id": "Agent:CapacityReport", "measured": {"height": 80, "width": 200}, "position": {"x": 1204.3133534743204, "y": 252.93583081570998}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"clean_html": false, "headers": "{\n  \"Accept\": \"application/json\",\n  \"Content-Type\": \"application/json\"\n}", "method": "POST", "outputs": {"result": {"type": "string", "value": ""}}, "proxy": "", "timeout": 120, "url": "http://************:5000/api/export_word", "variables": []}, "label": "Invoke", "name": "HTTP 请求_导出Word"}, "dragging": false, "id": "Invoke:ExportWord", "measured": {"height": 56, "width": 200}, "position": {"x": 1512.4752870090635, "y": -155.63407854984894}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "ragNode"}, {"data": {"form": {"content": [" {Agent:CapacityReport@content}"]}, "label": "Message", "name": "回复消息_0"}, "id": "Message:AfraidHousesInvent", "measured": {"height": 56, "width": 200}, "position": {"x": 1781.7960120845923, "y": 169.14404833836858}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "messageNode", "dragging": false}, {"id": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "agentNode", "position": {"x": 691.4318206195527, "y": 314.91274110281074}, "data": {"label": "Agent", "name": "智能体_0", "form": {"temperatureEnabled": false, "topPEnabled": false, "presencePenaltyEnabled": false, "frequencyPenaltyEnabled": false, "maxTokensEnabled": false, "temperature": 0.1, "top_p": 0.3, "frequency_penalty": 0.7, "presence_penalty": 0.4, "max_tokens": 256, "description": "", "user_prompt": "", "sys_prompt": "<role>\n  You are {{agent_name}}, an AI assistant specialized in {{domain_or_task}}.\n</role>\n<instructions>\n  1. Understand the user’s request.  \n  2. Decompose it into logical subtasks.  \n  3. Execute each subtask step by step, reasoning transparently.  \n  4. Validate accuracy and consistency.  \n  5. Summarize the final result clearly.\n</instructions>", "prompts": [{"role": "user", "content": "{sys.query}"}], "message_history_window_size": 12, "max_retries": 3, "delay_after_error": 1, "visual_files_var": "", "max_rounds": 1, "exception_method": "", "exception_goto": [], "exception_default_value": "", "tools": [], "mcp": [], "outputs": {"content": {"type": "string", "value": ""}}, "llm_id": "Qwen/Qwen3-235B-A22B@SILICONFLOW"}}, "sourcePosition": "right", "targetPosition": "left", "measured": {"width": 200, "height": 80}, "selected": false, "dragging": false}, {"id": "Message:FiveTreesFall", "type": "messageNode", "position": {"x": 688.2582879850227, "y": 538.7400757431474}, "data": {"label": "Message", "name": "回复消息_1", "form": {"content": [" {Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@content}"]}}, "sourcePosition": "right", "targetPosition": "left", "measured": {"width": 200, "height": 56}, "selected": false, "dragging": false}], "edges": [{"data": {"isHovered": false}, "id": "xy-edge__beginstart-Categorize:CapacityTypeend", "source": "begin", "sourceHandle": "start", "target": "Categorize:CapacityType", "targetHandle": "end"}, {"id": "xy-edge__Categorize:CapacityTypestart-Invoke:GetCapacityDataend", "source": "Categorize:CapacityType", "sourceHandle": "start", "target": "Invoke:GetCapacityData", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Invoke:GetCapacityDatastart-Agent:CapacityReportend", "source": "Invoke:GetCapacityData", "sourceHandle": "start", "target": "Agent:CapacityReport", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:CapacityReportstart-Invoke:ExportWordend", "source": "Agent:CapacityReport", "sourceHandle": "start", "target": "Invoke:ExportWord", "targetHandle": "end"}, {"id": "xy-edge__Invoke:ExportWordstart-Message:AfraidHousesInventend", "source": "Invoke:ExportWord", "sourceHandle": "start", "target": "Message:AfraidHousesInvent", "targetHandle": "end", "data": {"isHovered": false}}, {"data": {"isHovered": false}, "id": "xy-edge__Categorize:CapacityType11111111-1111-1111-1111-111111111111-Invoke:GetCapacityDataend", "markerEnd": "logo", "source": "Categorize:CapacityType", "sourceHandle": "11111111-1111-1111-1111-111111111111", "style": {"stroke": "rgba(151, 154, 171, 1)", "strokeWidth": 1}, "target": "Invoke:GetCapacityData", "targetHandle": "end", "type": "buttonEdge", "zIndex": 1001}, {"data": {"isHovered": false}, "id": "xy-edge__Categorize:CapacityType*************-2222-2222-************-Invoke:GetCapacityDataend", "markerEnd": "logo", "source": "Categorize:CapacityType", "sourceHandle": "*************-2222-2222-************", "style": {"stroke": "rgba(151, 154, 171, 1)", "strokeWidth": 1}, "target": "Invoke:GetCapacityData", "targetHandle": "end", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Categorize:CapacityType*************-3333-3333-************-Invoke:GetCapacityDataend", "markerEnd": "logo", "source": "Categorize:CapacityType", "sourceHandle": "*************-3333-3333-************", "style": {"stroke": "rgba(151, 154, 171, 1)", "strokeWidth": 1}, "target": "Invoke:GetCapacityData", "targetHandle": "end", "type": "buttonEdge", "zIndex": 1001, "data": {"isHovered": false}}, {"data": {"isHovered": false}, "id": "xy-edge__Categorize:CapacityType*************-4444-4444-************-Invoke:GetCapacityDataend", "markerEnd": "logo", "source": "Categorize:CapacityType", "sourceHandle": "*************-4444-4444-************", "style": {"stroke": "rgba(151, 154, 171, 1)", "strokeWidth": 1}, "target": "Invoke:GetCapacityData", "targetHandle": "end", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Categorize:CapacityType*************-5555-5555-************-Invoke:GetCapacityDataend", "markerEnd": "logo", "source": "Categorize:CapacityType", "sourceHandle": "*************-5555-5555-************", "style": {"stroke": "rgba(151, 154, 171, 1)", "strokeWidth": 1}, "target": "Invoke:GetCapacityData", "targetHandle": "end", "type": "buttonEdge", "zIndex": 1001, "data": {"isHovered": false}}, {"source": "Categorize:CapacityType", "target": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceHandle": "d69b6476-1bcd-473d-ba39-4d4f2fb50655", "targetHandle": "end", "id": "xy-edge__Categorize:CapacityTyped69b6476-1bcd-473d-ba39-4d4f2fb50655-Agent:SilentWolvesLaughend", "data": {"isHovered": false}}, {"source": "Agent:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target": "Message:FiveTreesFall", "sourceHandle": "start", "targetHandle": "end", "id": "xy-edge__Agent:SilentWolvesLaughstart-Message:FiveTreesFallend", "data": {"isHovered": false}}]}