{"name": "client-cli-example", "version": "0.1.0", "private": true, "scripts": {"start": "tsx src/index.ts", "dev": "tsx --watch src/index.ts", "build": "tsc", "clean": "rm -rf dist"}, "dependencies": {"@ag-ui/client": "workspace:*", "@ag-ui/core": "workspace:*", "@ag-ui/mastra": "workspace:*", "@ai-sdk/openai": "1.3.22", "@mastra/client-js": "0.10.18", "@mastra/core": "0.12.1", "@mastra/libsql": "0.12.0", "@mastra/loggers": "0.10.5", "@mastra/memory": "0.12.0", "open": "^10.1.2", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20", "tsx": "^4.7.0", "typescript": "^5"}}