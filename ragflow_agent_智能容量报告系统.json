{"nodes": [{"data": {"form": {"enablePrologue": true, "inputs": {}, "mode": "conversational", "prologue": "你好！ 我是你的助理，有什么可以帮到你的吗？"}, "label": "<PERSON><PERSON>", "name": "begin"}, "id": "begin", "measured": {"height": 48, "width": 200}, "position": {"x": -18, "y": 20}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode", "dragging": false}, {"data": {"form": {"delay_after_error": 1, "description": "根据用户自然语言需求判断报告类型，仅返回类型字符串（如 database 或 storage,virtualization）。", "exception_default_value": "", "exception_goto": [], "exception_method": "", "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "Qwen/Qwen3-235B-A22B@SILICONFLOW", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 1, "max_tokens": 256, "mcp": [], "message_history_window_size": 1, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "{sys.query}", "role": "user"}], "sys_prompt": "你是一个专业的IT容量报告需求分析专家，负责分析用户的自然语言需求，并判断应该生成哪种类型的容量报告。\n\n支持的报告类型（小写）：all, storage, database, container, virtualization，或多种类型用英文逗号组合（如 storage,virtualization）。\n分析规则：若提到‘完整/全面/所有/整体’选 all；提到‘存储/磁盘/硬盘’含 storage；提到‘数据库/DB/MySQL/Oracle’含 database；提到‘容器/Docker/Kubernetes/K8s’含 container；提到‘虚拟化/虚拟机/VM/ESX/vSphere’含 virtualization；多维度则组合；不明确则 all。\n输出要求：只输出最终类型字符串（小写），不要输出解释、标点或JSON。\n示例：\nall\nstorage\ndatabase\nstorage,virtualization", "temperature": 0.1, "temperatureEnabled": false, "tools": [], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": ""}, "label": "Agent", "name": "Agent_问题分类"}, "dragging": false, "id": "Agent:Classifier", "measured": {"height": 80, "width": 200}, "position": {"x": 569.1293636405858, "y": -211.12839815786654}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"clean_html": false, "headers": "{\n  \"Accept\": \"application/json\",\n  \"Content-Type\": \"application/json\"\n}", "method": "POST", "outputs": {"result": {"type": "string", "value": ""}}, "proxy": "", "timeout": 60, "url": "http://************:5000/api/get_capacity_data", "variables": []}, "label": "Invoke", "name": "HTTP 请求_获取容量数据"}, "dragging": false, "id": "Invoke:GetCapacityData", "measured": {"height": 56, "width": 200}, "position": {"x": 927.9099013710895, "y": -91.69282404214505}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "ragNode"}, {"data": {"form": {"delay_after_error": 1, "description": "通过对话方式生成智能容量报告，支持多轮交互与组合查询；保留HTTP数据获取与Word导出步骤。", "exception_default_value": "", "exception_goto": [], "exception_method": "", "frequencyPenaltyEnabled": false, "frequency_penalty": 0.7, "llm_id": "Qwen/Qwen3-235B-A22B@SILICONFLOW", "maxTokensEnabled": false, "max_retries": 3, "max_rounds": 1, "max_tokens": 512, "mcp": [], "message_history_window_size": 12, "outputs": {"content": {"type": "string", "value": ""}}, "presencePenaltyEnabled": false, "presence_penalty": 0.4, "prompts": [{"content": "{sys.query}", "role": "user"}], "sys_prompt": "你是一名专业的IT容量规划专家，擅长分析存储、数据库、容器、虚拟化等资源的容量使用情况并生成专业报告。\n\n要求：\n- 章节编号从1开始；组合按 storage -> database -> container -> virtualization 顺序；多维度最后增加总体风险评估。\n- 基于阈值进行健康度评估：存储<90/90~95/>95；数据库CPU/内存<70/70~85/>85；容器CPU/内存<80/80~90/>90；虚拟化CPU/内存<75/75~85/>85，存储<90/90~95/>95。\n- 输出 Markdown 表格，结论清晰专业。", "temperature": 0.1, "temperatureEnabled": false, "tools": [], "topPEnabled": false, "top_p": 0.3, "user_prompt": "", "visual_files_var": ""}, "label": "Agent", "name": "Agent_容量报告分析"}, "dragging": false, "id": "Agent:CapacityReport", "measured": {"height": 80, "width": 200}, "position": {"x": 1204.3133534743204, "y": 252.93583081570998}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "agentNode"}, {"data": {"form": {"clean_html": false, "headers": "{\n  \"Accept\": \"*/*\",\n  \"Content-Type\": \"application/x-www-form-urlencoded\"\n}", "method": "POST", "outputs": {"result": {"type": "string", "value": ""}}, "proxy": "", "timeout": 120, "url": "http://************:5000/api/export_word", "variables": [{"name": "report_content", "value": "{Agent:CapacityReport@content}"}, {"name": "system_name", "value": "智能容量报告系统(对话版)容量报告"}, {"name": "save_path", "value": "D:/work/LLM/reports/"}]}, "label": "Invoke", "name": "HTTP 请求_导出Word"}, "dragging": false, "id": "Invoke:ExportWord", "measured": {"height": 56, "width": 200}, "position": {"x": 1512.4752870090635, "y": -155.63407854984894}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "ragNode"}, {"data": {"form": {"content": [" {Agent:CapacityReport@content}"]}, "label": "Message", "name": "回复消息_0"}, "id": "Message:AfraidHousesInvent", "measured": {"height": 56, "width": 200}, "position": {"x": 1781.7960120845923, "y": 169.14404833836858}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "messageNode", "dragging": false}], "edges": [{"data": {"isHovered": false}, "id": "xy-edge__beginstart-Agent:Classifierend", "source": "begin", "sourceHandle": "start", "target": "Agent:Classifier", "targetHandle": "end"}, {"id": "xy-edge__Agent:Classifierstart-Invoke:GetCapacityDataend", "source": "Agent:Classifier", "sourceHandle": "start", "target": "Invoke:GetCapacityData", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Invoke:GetCapacityDatastart-Agent:CapacityReportend", "source": "Invoke:GetCapacityData", "sourceHandle": "start", "target": "Agent:CapacityReport", "targetHandle": "end"}, {"data": {"isHovered": false}, "id": "xy-edge__Agent:CapacityReportstart-Invoke:ExportWordend", "source": "Agent:CapacityReport", "sourceHandle": "start", "target": "Invoke:ExportWord", "targetHandle": "end"}, {"id": "xy-edge__Invoke:ExportWordstart-Message:AfraidHousesInventend", "source": "Invoke:ExportWord", "sourceHandle": "start", "target": "Message:AfraidHousesInvent", "targetHandle": "end", "data": {"isHovered": false}}]}