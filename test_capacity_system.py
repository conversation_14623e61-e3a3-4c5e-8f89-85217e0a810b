#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
容量数据收集系统测试脚本
用于验证系统各组件功能
"""

import os
import sys
import json
import requests
import pymysql
from datetime import date
from capacity_collector_config import API_CONFIG, DATABASE_CONFIG

def test_api_connectivity():
    """测试API连通性"""
    print("🔍 测试API连通性...")
    
    base_url = API_CONFIG['base_url']
    endpoints = ['/api/storage', '/api/database', '/api/container', '/api/virtualization']
    
    results = {}
    for endpoint in endpoints:
        try:
            url = f"{base_url}{endpoint}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                status = data.get('status', 'unknown')
                results[endpoint] = {'success': True, 'status': status}
                print(f"  ✅ {endpoint}: {status}")
            else:
                results[endpoint] = {'success': False, 'error': f'HTTP {response.status_code}'}
                print(f"  ❌ {endpoint}: HTTP {response.status_code}")
                
        except Exception as e:
            results[endpoint] = {'success': False, 'error': str(e)}
            print(f"  ❌ {endpoint}: {e}")
    
    success_count = sum(1 for r in results.values() if r['success'])
    print(f"API连通性测试完成: {success_count}/{len(endpoints)} 成功\n")
    
    return results

def test_database_connectivity():
    """测试数据库连通性"""
    print("🔍 测试数据库连通性...")
    
    try:
        conn = pymysql.connect(**DATABASE_CONFIG)
        cursor = conn.cursor()

        # 测试基本查询
        cursor.execute("SELECT VERSION();")
        version = cursor.fetchone()[0]
        print(f"  ✅ MySQL数据库连接成功")
        print(f"  📊 数据库版本: {version[:50]}...")

        # 检查表是否存在
        tables = [
            'storage_capacity_history',
            'database_capacity_history',
            'container_capacity_history',
            'virtualization_capacity_history',
            'capacity_collection_log'
        ]

        existing_tables = []
        for table in tables:
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = %s AND table_name = %s
            """, (DATABASE_CONFIG['database'], table))

            exists = cursor.fetchone()[0] > 0
            if exists:
                existing_tables.append(table)
                print(f"  ✅ 表 {table} 存在")
            else:
                print(f"  ❌ 表 {table} 不存在")
        
        cursor.close()
        conn.close()
        
        print(f"数据库连通性测试完成: {len(existing_tables)}/{len(tables)} 表存在\n")
        
        return {'success': True, 'existing_tables': existing_tables}
        
    except Exception as e:
        print(f"  ❌ 数据库连接失败: {e}\n")
        return {'success': False, 'error': str(e)}

def test_data_collection():
    """测试数据收集功能"""
    print("🔍 测试数据收集功能...")
    
    try:
        # 导入收集器
        from daily_capacity_collector import CapacityCollector
        
        collector = CapacityCollector()
        
        if not collector.connect_db():
            print("  ❌ 数据库连接失败")
            return {'success': False, 'error': '数据库连接失败'}
        
        # 测试各类型数据收集
        results = {}
        
        print("  📊 测试存储数据收集...")
        results['storage'] = collector.collect_storage_data()
        
        print("  📊 测试数据库数据收集...")
        results['database'] = collector.collect_database_data()
        
        print("  📊 测试容器数据收集...")
        results['container'] = collector.collect_container_data()
        
        print("  📊 测试虚拟化数据收集...")
        results['virtualization'] = collector.collect_virtualization_data()
        
        collector.disconnect_db()
        
        success_count = sum(1 for r in results.values() if r)
        print(f"数据收集测试完成: {success_count}/{len(results)} 成功\n")
        
        return {'success': success_count > 0, 'results': results}
        
    except Exception as e:
        print(f"  ❌ 数据收集测试失败: {e}\n")
        return {'success': False, 'error': str(e)}

def test_data_analysis():
    """测试数据分析功能"""
    print("🔍 测试数据分析功能...")
    
    try:
        from capacity_data_analyzer import CapacityAnalyzer
        
        analyzer = CapacityAnalyzer()
        
        if not analyzer.connect_db():
            print("  ❌ 数据库连接失败")
            return {'success': False, 'error': '数据库连接失败'}
        
        # 测试汇总查询
        print("  📊 测试每日汇总查询...")
        summary = analyzer.get_daily_summary()
        print(f"  📈 获取到 {len(summary)} 条汇总记录")
        
        # 测试趋势分析
        print("  📊 测试趋势分析...")
        try:
            trend = analyzer.analyze_usage_trend('storage', days=7)
            print(f"  📈 存储趋势: {trend.get('message', '无数据')}")
        except Exception as e:
            print(f"  ⚠️ 趋势分析: {e}")
        
        # 测试报告生成
        print("  📊 测试报告生成...")
        report = analyzer.generate_capacity_report(days=7)
        print(f"  📄 报告长度: {len(report)} 字符")
        
        analyzer.disconnect_db()
        
        print("数据分析测试完成\n")
        return {'success': True}
        
    except Exception as e:
        print(f"  ❌ 数据分析测试失败: {e}\n")
        return {'success': False, 'error': str(e)}

def generate_test_report():
    """生成测试报告"""
    print("=" * 60)
    print("容量数据收集系统测试报告")
    print("=" * 60)
    
    # 执行各项测试
    api_result = test_api_connectivity()
    db_result = test_database_connectivity()
    collection_result = test_data_collection()
    analysis_result = test_data_analysis()
    
    # 汇总结果
    print("📋 测试结果汇总:")
    print(f"  API连通性: {'✅ 通过' if any(r['success'] for r in api_result.values()) else '❌ 失败'}")
    print(f"  数据库连通性: {'✅ 通过' if db_result['success'] else '❌ 失败'}")
    print(f"  数据收集功能: {'✅ 通过' if collection_result['success'] else '❌ 失败'}")
    print(f"  数据分析功能: {'✅ 通过' if analysis_result['success'] else '❌ 失败'}")
    
    # 系统状态评估
    all_tests = [
        any(r['success'] for r in api_result.values()),
        db_result['success'],
        collection_result['success'],
        analysis_result['success']
    ]
    
    success_rate = sum(all_tests) / len(all_tests) * 100
    
    print(f"\n🎯 系统整体状态: {success_rate:.0f}% 功能正常")
    
    if success_rate >= 75:
        print("✅ 系统状态良好，可以正常使用")
    elif success_rate >= 50:
        print("⚠️ 系统部分功能异常，建议检查配置")
    else:
        print("❌ 系统存在严重问题，需要修复后使用")
    
    print("=" * 60)

def main():
    """主函数"""
    print("🚀 启动容量数据收集系统测试...\n")
    
    # 检查配置
    print("🔧 检查系统配置...")
    print(f"  API地址: {API_CONFIG['base_url']}")
    print(f"  数据库: {DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}")
    print()
    
    # 生成测试报告
    generate_test_report()

if __name__ == '__main__':
    main()
