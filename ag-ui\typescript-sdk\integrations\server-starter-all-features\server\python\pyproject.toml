[tool.poetry]
name = "example_server"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.12"
ag-ui-protocol = {path = "../../../../../python-sdk/"}
fastapi = "^0.115.12"
uvicorn = "^0.34.3"
jsonpatch = "^1.33"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
dev = "example_server:main"