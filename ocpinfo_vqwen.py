from flask import Flask, jsonify
import requests
from functools import lru_cache
import time

# ========================
# 配置
# ========================
PROMETHEUS_URL = "http://10.5.200.10:8428/api/v1/query"

# 集群 ID 到名称的映射（根据你的实际情况修改）
CLUSTER_NAME_MAP = {
    "JX-K8S-PROD": "嘉兴K8S生产集群",
    "HSY-K8S-PROD": "后沙峪K8S生产集群",
    "JX-K8S-TEST": "嘉兴K8S测试集群",
    "HSY-TAP-PROD": "后沙峪TAP容器集群",
    # 可继续添加
}

# 默认超时
TIMEOUT = 10

# 创建 Flask 应用
app = Flask(__name__)

# ========================
# 工具函数
# ========================
def bytes_to_gb(bytes_value):
    """将字节转换为GB"""
    return round(bytes_value / (1024 ** 3), 2)

def query_prometheus(query):
    """查询 Prometheus 数据"""
    try:
        response = requests.get(PROMETHEUS_URL, params={'query': query}, timeout=TIMEOUT)
        response.raise_for_status()
        data = response.json()
        if data.get("status") == "success":
            return data.get("data", {}).get("result", [])
        else:
            print(f"Prometheus 查询失败 [{query}]: {data.get('error', 'Unknown error')}")
            return []
    except Exception as e:
        print(f"请求 Prometheus 失败 [{query}]: {e}")
        return []

# ========================
# 数据获取函数（带缓存）
# ========================
@lru_cache(maxsize=1)
def fetch_data_cached():
    """带缓存的数据获取函数"""
    return fetch_data()

def get_clusters_data():
    """获取集群数据，每60秒清除一次缓存"""
    # 每隔60秒清除缓存
    current_time = int(time.time())
    if current_time % 60 == 0:
        fetch_data_cached.cache_clear()
    return fetch_data_cached()

def fetch_data():
    """从 Prometheus 获取集群监控数据"""
    # 存储最终结果
    clusters = []

    # 1. 获取每个集群的节点数和 CPU 核数（总核数）
    cpu_cores_query = '''
        sum by (cluster_id) (
            count without (cpu) (
                node_cpu_seconds_total{job="node-exporter"}
            )
        )
    '''
    node_count_query = '''
        count by (cluster_id) (
            count by (cluster_id, instance) (
                node_cpu_seconds_total{job="node-exporter"}
            )
        )
    '''

    # 2. 获取 CPU 使用率（非 idle 时间）
    cpu_usage_query = '''
        sum by (cluster_id) (
            rate(node_cpu_seconds_total{job="node-exporter", mode!="idle"}[5m])
        )
    '''
    cpu_total_query = '''
        sum by (cluster_id) (
            rate(node_cpu_seconds_total{job="node-exporter"}[5m])
        )
    '''

    # 3. 内存使用
    memory_total_query = '''
        sum by (cluster_id) (
            node_memory_MemTotal_bytes{job="node-exporter"}
        )
    '''
    memory_free_query = '''
        sum by (cluster_id) (
            node_memory_MemAvailable_bytes{job="node-exporter"}
        )
    '''

    # 4. 存储使用（文件系统）
    storage_total_query = '''
        sum by (cluster_id) (
            node_filesystem_size_bytes{job="node-exporter", mountpoint="/", fstype!=""}
        )
    '''
    storage_free_query = '''
        sum by (cluster_id) (
            node_filesystem_avail_bytes{job="node-exporter", mountpoint="/", fstype!=""}
        )
    '''

    # 执行查询
    cpu_cores_list = query_prometheus(cpu_cores_query)
    node_count_list = query_prometheus(node_count_query)
    cpu_usage_list = query_prometheus(cpu_usage_query)
    cpu_total_list = query_prometheus(cpu_total_query)
    memory_total_list = query_prometheus(memory_total_query)
    memory_free_list = query_prometheus(memory_free_query)
    storage_total_list = query_prometheus(storage_total_query)
    storage_free_list = query_prometheus(storage_free_query)

    # 构建 cluster_id -> 数据 映射
    def list_to_dict(data_list):
        return {item['metric']['cluster_id']: float(item['value'][1]) for item in data_list}

    cpu_cores_map = list_to_dict(cpu_cores_list)
    node_count_map = list_to_dict(node_count_list)
    cpu_usage_map = list_to_dict(cpu_usage_list)
    cpu_total_map = list_to_dict(cpu_total_list)
    memory_total_map = list_to_dict(memory_total_list)
    memory_free_map = list_to_dict(memory_free_list)
    storage_total_map = list_to_dict(storage_total_list)
    storage_free_map = list_to_dict(storage_free_list)

    # 所有出现的 cluster_id
    all_cluster_ids = set()
    for d in [cpu_cores_map, node_count_map, memory_total_map, storage_total_map]:
        all_cluster_ids.update(d.keys())

    # 构造返回结果
    for cluster_id in all_cluster_ids:
        try:
            total_cores = cpu_cores_map.get(cluster_id, 0)
            node_count = int(node_count_map.get(cluster_id, 0))
            cpu_used = cpu_usage_map.get(cluster_id, 0)
            cpu_total = cpu_total_map.get(cluster_id, 1)  # 防止除零
            cpu_usage_rate = round((cpu_used / cpu_total) * 100, 2) if cpu_total > 0 else 0
            cpu_used_cores = round(total_cores * cpu_usage_rate / 100, 2)

            memory_total_bytes = memory_total_map.get(cluster_id, 0)
            memory_free_bytes = memory_free_map.get(cluster_id, 0)
            memory_used_bytes = max(0, memory_total_bytes - memory_free_bytes)
            memory_total_gb = bytes_to_gb(memory_total_bytes)
            memory_used_gb = bytes_to_gb(memory_used_bytes)

            storage_total_bytes = storage_total_map.get(cluster_id, 0)
            storage_free_bytes = storage_free_map.get(cluster_id, 0)
            storage_used_bytes = max(0, storage_total_bytes - storage_free_bytes)
            storage_total_gb = bytes_to_gb(storage_total_bytes)
            storage_used_gb = bytes_to_gb(storage_used_bytes)

            cluster_name = CLUSTER_NAME_MAP.get(cluster_id, cluster_id)  # 默认用 ID

            clusters.append({
                "cluster_name": cluster_name,
                "cluster_id": cluster_id,
                "cpu_total_cores": round(total_cores, 2),
                "cpu_used_cores": cpu_used_cores,
                "cpu_usage_rate": cpu_usage_rate,
                "memory_total_gb": memory_total_gb,
                "memory_used_gb": memory_used_gb,
                "storage_total_gb": storage_total_gb,
                "storage_used_gb": storage_used_gb,
                "node_count": node_count
            })
        except Exception as e:
            print(f"处理 cluster_id={cluster_id} 时出错: {e}")
            continue

    return clusters

# ========================
# API 路由
# ========================
@app.route('/api/clusters', methods=['GET'])
def get_clusters():
    """获取所有集群的监控数据"""
    try:
        data = get_clusters_data()
        return jsonify(data), 200
    except Exception as e:
        print(f"API 错误: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({"status": "healthy"}), 200

# ========================
# 主程序入口
# ========================
if __name__ == '__main__':
    print("启动 Prometheus 监控 API 服务...")
    print(f"访问 http://localhost:5000/api/clusters")
    app.run(host='0.0.0.0', port=5000, debug=False)