syntax = "proto3";

package ag_ui;

message ToolCall {
  string id = 1;
  string type = 2; 
  message Function {
    string name = 1;
    string arguments = 2; 
  }  
  Function function = 3;
}

message Message {
  string id = 1;
  string role = 2;
  optional string content = 3;
  optional string name = 4;
  repeated ToolCall tool_calls = 5;
  optional string tool_call_id = 6;
  optional string error = 7;
}
