#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL容量数据收集系统一键部署脚本
"""

import os
import sys
import subprocess
import time
import pymysql
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# MySQL配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 13306,
    'database': 'prod',
    'user': 'mrms',
    'password': 'kfaccess',
    'charset': 'utf8mb4'
}

DOCKER_COMMAND = """
docker run --name report_mysql \
  -e MYSQL_ROOT_PASSWORD=Prodaccess_1! \
  -e MYSQL_DATABASE=prod \
  -e MYSQL_USER=mrms \
  -e MYSQL_PASSWORD=kfaccess \
  -p 13306:3306 \
  -v mysql-data:/var/lib/mysql \
  -d mysql:8.0.43-debian
""".strip()

def check_docker():
    """检查Docker是否安装"""
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"✅ Docker已安装: {result.stdout.strip()}")
            return True
        else:
            logger.error("❌ Docker未安装或无法访问")
            return False
    except FileNotFoundError:
        logger.error("❌ Docker未安装")
        return False

def check_mysql_container():
    """检查MySQL容器是否存在"""
    try:
        result = subprocess.run(['docker', 'ps', '-a', '--filter', 'name=report_mysql'], 
                              capture_output=True, text=True)
        if 'report_mysql' in result.stdout:
            logger.info("📦 MySQL容器已存在")
            return True
        else:
            logger.info("📦 MySQL容器不存在")
            return False
    except Exception as e:
        logger.error(f"检查容器失败: {e}")
        return False

def start_mysql_container():
    """启动MySQL容器"""
    try:
        logger.info("🚀 启动MySQL容器...")
        
        # 检查容器是否已存在
        if check_mysql_container():
            # 尝试启动现有容器
            result = subprocess.run(['docker', 'start', 'report_mysql'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✅ MySQL容器启动成功")
                return True
            else:
                logger.warning("⚠️ 启动现有容器失败，尝试删除并重新创建")
                subprocess.run(['docker', 'rm', '-f', 'report_mysql'])
        
        # 创建新容器
        result = subprocess.run(DOCKER_COMMAND.split(), capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ MySQL容器创建成功")
            
            # 等待MySQL启动
            logger.info("⏳ 等待MySQL启动...")
            time.sleep(30)
            
            return True
        else:
            logger.error(f"❌ MySQL容器创建失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"启动MySQL容器失败: {e}")
        return False

def test_mysql_connection():
    """测试MySQL连接"""
    max_retries = 10
    retry_interval = 5
    
    for i in range(max_retries):
        try:
            logger.info(f"🔍 测试MySQL连接 (尝试 {i+1}/{max_retries})...")
            conn = pymysql.connect(**MYSQL_CONFIG)
            cursor = conn.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            cursor.close()
            conn.close()
            
            logger.info(f"✅ MySQL连接成功，版本: {version}")
            return True
            
        except Exception as e:
            logger.warning(f"⚠️ MySQL连接失败: {e}")
            if i < max_retries - 1:
                logger.info(f"⏳ {retry_interval}秒后重试...")
                time.sleep(retry_interval)
            else:
                logger.error("❌ MySQL连接失败，已达到最大重试次数")
                return False
    
    return False

def create_database_tables():
    """创建数据库表"""
    try:
        logger.info("📊 创建数据库表...")
        
        # 读取DDL文件
        ddl_file = 'capacity_tables_ddl.sql'
        if not os.path.exists(ddl_file):
            logger.error(f"❌ DDL文件不存在: {ddl_file}")
            return False
        
        with open(ddl_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 连接数据库并执行DDL
        conn = pymysql.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()
        
        # 分割SQL语句并执行
        statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        for statement in statements:
            if statement.upper().startswith(('CREATE', 'ALTER', 'SET')):
                try:
                    cursor.execute(statement)
                    logger.debug(f"执行SQL: {statement[:50]}...")
                except Exception as e:
                    logger.warning(f"SQL执行警告: {e}")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info("✅ 数据库表创建完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建数据库表失败: {e}")
        return False

def install_python_dependencies():
    """安装Python依赖"""
    try:
        logger.info("📦 安装Python依赖...")
        
        dependencies = [
            'pymysql>=1.0.2',
            'pandas>=1.5.0',
            'matplotlib>=3.5.0',
            'seaborn>=0.11.0',
            'openpyxl>=3.0.0',
            'schedule>=1.2.0',
            'requests>=2.28.0'
        ]
        
        for dep in dependencies:
            result = subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ 安装成功: {dep}")
            else:
                logger.warning(f"⚠️ 安装失败: {dep}")
        
        logger.info("✅ Python依赖安装完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 安装Python依赖失败: {e}")
        return False

def run_system_test():
    """运行系统测试"""
    try:
        logger.info("🧪 运行系统测试...")
        
        result = subprocess.run([sys.executable, 'test_capacity_system.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ 系统测试通过")
            logger.info("测试输出:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    logger.info(f"  {line}")
            return True
        else:
            logger.error("❌ 系统测试失败")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 运行系统测试失败: {e}")
        return False

def main():
    """主部署流程"""
    logger.info("=" * 60)
    logger.info("MySQL容量数据收集系统一键部署")
    logger.info("=" * 60)
    
    # 检查前置条件
    if not check_docker():
        logger.error("请先安装Docker")
        return False
    
    # 安装Python依赖
    if not install_python_dependencies():
        logger.error("Python依赖安装失败")
        return False
    
    # 启动MySQL容器
    if not start_mysql_container():
        logger.error("MySQL容器启动失败")
        return False
    
    # 测试MySQL连接
    if not test_mysql_connection():
        logger.error("MySQL连接测试失败")
        return False
    
    # 创建数据库表
    if not create_database_tables():
        logger.error("数据库表创建失败")
        return False
    
    # 运行系统测试
    if not run_system_test():
        logger.error("系统测试失败")
        return False
    
    logger.info("=" * 60)
    logger.info("🎉 部署完成！")
    logger.info("=" * 60)
    logger.info("下一步操作:")
    logger.info("1. 手动执行数据收集: python daily_capacity_collector.py")
    logger.info("2. 设置定时任务: python schedule_capacity_collector.py --mode install-windows")
    logger.info("3. 数据分析: python capacity_data_analyzer.py")
    logger.info("4. MySQL连接信息:")
    logger.info(f"   主机: {MYSQL_CONFIG['host']}")
    logger.info(f"   端口: {MYSQL_CONFIG['port']}")
    logger.info(f"   数据库: {MYSQL_CONFIG['database']}")
    logger.info(f"   用户: {MYSQL_CONFIG['user']}")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("用户中断部署")
        sys.exit(1)
    except Exception as e:
        logger.error(f"部署过程中出现异常: {e}")
        sys.exit(1)
