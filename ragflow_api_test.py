import requests
import json

def call_api():
    # API 配置
    url = "http://172.30.224.1/api/v1/agents/7a31eb4c791011f0b0a2528d52db5855/completions"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ragflow-RiOTdjYTk0Nzk4MDExZjA5ZDI0OTZiND'
    }
    data = {
        "question": "帮我检查存储和虚拟化的使用情况",
        "stream": False
    }

    try:
        # 发送 POST 请求
        response = requests.post(
            url=url,
            headers=headers,
            json=data,
            stream=True  # 启用流式传输以处理 stream=True 的响应
        )
        
        # 检查响应状态
        response.raise_for_status()
        
        # 处理流式响应
        print("Response:")
        for line in response.iter_lines():
            if line:
                decoded_line = line.decode('utf-8')
                print(decoded_line)
                
    except requests.exceptions.RequestException as e:
        print(f"Error calling API: {e}")

if __name__ == "__main__":
    call_api()