#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL连接测试脚本
用于验证MySQL容器和数据库连接
"""

import pymysql
import sys
import time

# MySQL配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 13306,
    'database': 'prod',
    'user': 'mrms',
    'password': 'kfaccess',
    'charset': 'utf8mb4'
}

def test_connection():
    """测试MySQL连接"""
    try:
        print("🔍 测试MySQL连接...")
        print(f"连接信息: {MYSQL_CONFIG['user']}@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}/{MYSQL_CONFIG['database']}")
        
        conn = pymysql.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()
        
        # 测试基本查询
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"✅ MySQL连接成功")
        print(f"📊 数据库版本: {version}")
        
        # 测试数据库信息
        cursor.execute("SELECT DATABASE()")
        current_db = cursor.fetchone()[0]
        print(f"📂 当前数据库: {current_db}")
        
        # 检查字符集
        cursor.execute("SELECT @@character_set_database, @@collation_database")
        charset_info = cursor.fetchone()
        print(f"🔤 字符集: {charset_info[0]}, 排序规则: {charset_info[1]}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return False

def check_tables():
    """检查表是否存在"""
    try:
        print("\n🔍 检查数据库表...")
        
        conn = pymysql.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SHOW TABLES")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = [
            'storage_capacity_history',
            'database_capacity_history',
            'container_capacity_history',
            'virtualization_capacity_history',
            'capacity_collection_log'
        ]
        
        print(f"📊 数据库中的表 ({len(tables)} 个):")
        for table in tables:
            print(f"  ✅ {table}")
        
        print(f"\n📋 期望的表:")
        missing_tables = []
        for table in expected_tables:
            if table in tables:
                print(f"  ✅ {table} - 存在")
            else:
                print(f"  ❌ {table} - 缺失")
                missing_tables.append(table)
        
        if missing_tables:
            print(f"\n⚠️ 缺失 {len(missing_tables)} 个表，请运行DDL脚本创建表")
            return False
        else:
            print(f"\n✅ 所有表都存在")
            return True
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查表失败: {e}")
        return False

def test_insert_data():
    """测试插入数据"""
    try:
        print("\n🔍 测试数据插入...")
        
        conn = pymysql.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()
        
        # 测试插入一条记录
        test_sql = """
        INSERT INTO capacity_collection_log 
        (collection_date, collection_type, status, records_count, execution_time_seconds)
        VALUES (CURDATE(), 'test', 'SUCCESS', 1, 0.5)
        """
        
        cursor.execute(test_sql)
        conn.commit()
        
        # 查询刚插入的记录
        cursor.execute("""
        SELECT * FROM capacity_collection_log 
        WHERE collection_type = 'test' 
        ORDER BY created_at DESC LIMIT 1
        """)
        
        result = cursor.fetchone()
        if result:
            print("✅ 数据插入测试成功")
            print(f"📊 插入的记录: ID={result[0]}, 类型={result[2]}, 状态={result[3]}")
            
            # 清理测试数据
            cursor.execute("DELETE FROM capacity_collection_log WHERE collection_type = 'test'")
            conn.commit()
            print("🧹 测试数据已清理")
        else:
            print("❌ 未找到插入的测试数据")
            return False
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 数据插入测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("MySQL容量数据收集系统连接测试")
    print("=" * 60)
    
    # 测试连接
    if not test_connection():
        print("\n❌ MySQL连接测试失败，请检查:")
        print("1. MySQL容器是否启动: docker ps | grep report_mysql")
        print("2. 端口是否正确: 13306")
        print("3. 用户名密码是否正确")
        return False
    
    # 检查表
    tables_ok = check_tables()
    if not tables_ok:
        print("\n⚠️ 表结构不完整，请运行以下命令创建表:")
        print("mysql -h localhost -P 13306 -u mrms -pkfaccess prod < capacity_tables_ddl.sql")
        print("或运行: python deploy_mysql_capacity_system.py")
    
    # 测试数据操作
    if tables_ok:
        if not test_insert_data():
            return False
    
    print("\n" + "=" * 60)
    if tables_ok:
        print("🎉 所有测试通过！系统可以正常使用")
        print("\n下一步:")
        print("1. 运行数据收集: python daily_capacity_collector.py")
        print("2. 设置定时任务: python schedule_capacity_collector.py")
        print("3. 数据分析: python capacity_data_analyzer.py")
    else:
        print("⚠️ 部分测试通过，需要创建数据库表")
    print("=" * 60)
    
    return tables_ok

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中出现异常: {e}")
        sys.exit(1)
