{"name": "@ag-ui/proto", "author": "<PERSON> <<EMAIL>>", "version": "0.0.37", "private": false, "publishConfig": {"access": "public"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint \"src/**/*.ts*\"", "clean": "rm -rf dist .turbo node_modules", "test": "jest", "generate": "mkdir -p ./src/generated && npx protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_out=./src/generated --ts_proto_opt=esModuleInterop=true,outputJsonMethods=false,outputClientImpl=false -I ./src/proto ./src/proto/*.proto", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}, "dependencies": {"@ag-ui/core": "workspace:*", "@bufbuild/protobuf": "^2.2.5", "@protobuf-ts/protoc": "^2.11.1"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.14", "jest": "^29.7.0", "ts-jest": "^29.1.2", "ts-proto": "^2.7.0", "tsup": "^8.0.2", "typescript": "^5.8.2"}}