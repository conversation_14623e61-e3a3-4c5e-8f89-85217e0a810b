# MySQL容量数据收集系统部署指南

## 系统概述

本系统已适配MySQL数据库，使用Docker部署的MySQL 8.0.43作为数据存储后端。

## Docker MySQL配置

### 启动命令
```bash
docker run --name report_mysql \
  -e MYSQL_ROOT_PASSWORD=Prodaccess_1! \
  -e MYSQL_DATABASE=prod \
  -e MYSQL_USER=mrms \
  -e MYSQL_PASSWORD=kfaccess \
  -p 13306:3306 \
  -v mysql-data:/var/lib/mysql \
  -d mysql:8.0.43-debian
```

### 连接信息
- **主机**: localhost
- **端口**: 13306
- **数据库**: prod
- **用户名**: mrms
- **密码**: kfaccess

## 安装步骤

### 1. 启动MySQL容器
```bash
# 启动MySQL容器
docker run --name report_mysql \
  -e MYSQL_ROOT_PASSWORD=Prodaccess_1! \
  -e MYSQL_DATABASE=prod \
  -e MYSQL_USER=mrms \
  -e MYSQL_PASSWORD=kfaccess \
  -p 13306:3306 \
  -v mysql-data:/var/lib/mysql \
  -d mysql:8.0.43-debian

# 检查容器状态
docker ps | grep report_mysql

# 查看容器日志
docker logs report_mysql
```

### 2. 安装Python依赖
```bash
# 安装MySQL相关依赖
pip install pymysql pandas matplotlib seaborn openpyxl schedule

# 或使用requirements.txt
cat > requirements.txt << EOF
pymysql>=1.0.2
pandas>=1.5.0
matplotlib>=3.5.0
seaborn>=0.11.0
openpyxl>=3.0.0
schedule>=1.2.0
requests>=2.28.0
EOF

pip install -r requirements.txt
```

### 3. 创建数据库表
```bash
# 方法1: 使用MySQL客户端
mysql -h localhost -P 13306 -u mrms -pkfaccess prod < capacity_tables_ddl.sql

# 方法2: 使用Docker exec
docker exec -i report_mysql mysql -u mrms -pkfaccess prod < capacity_tables_ddl.sql

# 方法3: 使用Python脚本
python -c "
import pymysql
conn = pymysql.connect(host='localhost', port=13306, user='mrms', password='kfaccess', database='prod', charset='utf8mb4')
with open('capacity_tables_ddl.sql', 'r', encoding='utf-8') as f:
    sql_content = f.read()
    for statement in sql_content.split(';'):
        if statement.strip():
            conn.cursor().execute(statement)
    conn.commit()
    conn.close()
print('数据库表创建完成')
"
```

### 4. 验证安装
```bash
# 运行系统测试
python test_capacity_system.py
```

## 配置说明

### 数据库配置 (capacity_collector_config.py)
```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 13306,
    'database': 'prod',
    'user': 'mrms',
    'password': 'kfaccess',
    'charset': 'utf8mb4',
    'autocommit': False,
    'connect_timeout': 30
}
```

### API配置
```python
API_CONFIG = {
    'base_url': 'http://127.0.0.1:5002',  # app.py服务地址
    'timeout': 30,
    'retry_times': 3,
    'retry_delay': 5
}
```

## 使用方法

### 1. 手动执行数据收集
```bash
python daily_capacity_collector.py
```

### 2. 设置定时任务
```bash
# Windows
python schedule_capacity_collector.py --mode install-windows

# Linux
python schedule_capacity_collector.py --mode install-linux

# 内置调度器
python schedule_capacity_collector.py --mode schedule
```

### 3. 数据分析
```bash
python capacity_data_analyzer.py
```

## 数据库管理

### 连接数据库
```bash
# 使用MySQL客户端
mysql -h localhost -P 13306 -u mrms -pkfaccess prod

# 使用Docker exec
docker exec -it report_mysql mysql -u mrms -pkfaccess prod
```

### 常用查询
```sql
-- 查看所有表
SHOW TABLES;

-- 查看表结构
DESCRIBE storage_capacity_history;

-- 查看最新收集的数据
SELECT * FROM capacity_collection_log ORDER BY created_at DESC LIMIT 10;

-- 查看今日容量汇总
SELECT * FROM daily_capacity_summary WHERE record_date = CURDATE();

-- 查看存储容量趋势
SELECT record_date, pool_name, usage_rate 
FROM storage_capacity_history 
WHERE record_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY record_date, pool_name;
```

### 数据维护
```sql
-- 清理30天前的历史数据
DELETE FROM storage_capacity_history WHERE record_date < DATE_SUB(CURDATE(), INTERVAL 30 DAY);
DELETE FROM database_capacity_history WHERE record_date < DATE_SUB(CURDATE(), INTERVAL 30 DAY);
DELETE FROM container_capacity_history WHERE record_date < DATE_SUB(CURDATE(), INTERVAL 30 DAY);
DELETE FROM virtualization_capacity_history WHERE record_date < DATE_SUB(CURDATE(), INTERVAL 30 DAY);
DELETE FROM capacity_collection_log WHERE collection_date < DATE_SUB(CURDATE(), INTERVAL 30 DAY);

-- 优化表
OPTIMIZE TABLE storage_capacity_history;
OPTIMIZE TABLE database_capacity_history;
OPTIMIZE TABLE container_capacity_history;
OPTIMIZE TABLE virtualization_capacity_history;
OPTIMIZE TABLE capacity_collection_log;
```

## 故障排除

### 常见问题

1. **MySQL容器无法启动**
   ```bash
   # 检查端口占用
   netstat -an | grep 13306
   
   # 检查Docker日志
   docker logs report_mysql
   
   # 重启容器
   docker restart report_mysql
   ```

2. **数据库连接失败**
   ```bash
   # 测试连接
   mysql -h localhost -P 13306 -u mrms -pkfaccess -e "SELECT 1"
   
   # 检查防火墙
   telnet localhost 13306
   ```

3. **字符编码问题**
   ```sql
   -- 检查字符集
   SHOW VARIABLES LIKE 'character_set%';
   
   -- 设置字符集
   SET NAMES utf8mb4;
   ```

### 性能优化

1. **索引优化**
   ```sql
   -- 查看索引使用情况
   SHOW INDEX FROM storage_capacity_history;
   
   -- 分析查询性能
   EXPLAIN SELECT * FROM storage_capacity_history WHERE record_date = '2024-12-01';
   ```

2. **配置优化**
   ```bash
   # 修改MySQL配置 (可选)
   docker exec -it report_mysql bash
   echo "
   [mysqld]
   innodb_buffer_pool_size = 256M
   max_connections = 200
   query_cache_size = 64M
   " >> /etc/mysql/mysql.conf.d/mysqld.cnf
   
   # 重启容器使配置生效
   docker restart report_mysql
   ```

## 备份和恢复

### 数据备份
```bash
# 备份所有数据
docker exec report_mysql mysqldump -u mrms -pkfaccess prod > capacity_backup_$(date +%Y%m%d).sql

# 备份特定表
docker exec report_mysql mysqldump -u mrms -pkfaccess prod storage_capacity_history > storage_backup.sql
```

### 数据恢复
```bash
# 恢复数据
docker exec -i report_mysql mysql -u mrms -pkfaccess prod < capacity_backup_20241201.sql
```

## 监控和告警

### 容器监控
```bash
# 监控容器状态
docker stats report_mysql

# 查看容器资源使用
docker exec report_mysql df -h
docker exec report_mysql free -h
```

### 数据库监控
```sql
-- 查看数据库大小
SELECT 
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'prod'
GROUP BY table_schema;

-- 查看表大小
SELECT 
    table_name AS 'Table',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'prod'
ORDER BY (data_length + index_length) DESC;
```

现在您的容量数据收集系统已完全适配MySQL数据库，可以与Docker部署的MySQL容器配合使用！
