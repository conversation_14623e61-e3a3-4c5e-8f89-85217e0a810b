[tool.poetry]
name = "langgraph_agui_dojo"
version = "0.1.0"
description = ""
readme = "README.md"
packages = [{ include = "agents" }]

[project]
name = "agents"
version = "0.0.1"

[tool.poetry.dependencies]
python = ">=3.12,<3.14"
uvicorn = "^0.34.0"
dotenv = "^0.9.9"
langchain = ">=0.1.0"
langchain-anthropic = ">=0.3.18"
langchain-core = ">=0.1.5"
langchain-community = ">=0.0.1"
langchain-experimental = ">=0.0.11"
langchain-google-genai = ">=2.1.9"
langchain-openai = ">=0.0.1"
langgraph = "^0.6.1"
ag-ui-langgraph = { version = "0.0.10", extras = ["fastapi"] }
python-dotenv = "^1.0.0"
fastapi = "^0.115.12"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
dev = "agents.dojo:main"