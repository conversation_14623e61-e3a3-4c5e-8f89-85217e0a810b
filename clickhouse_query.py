#!/usr/bin/env python3
"""
ClickHouse 查询脚本（优先 HTTP，失败自动回退 TCP）

- 优先使用 clickhouse-connect 通过 HTTP/HTTPS 连接
- 若失败（例如端口为 TCP/Native），自动回退使用 clickhouse-driver（Native 协议）

依赖：
  pip install clickhouse-connect clickhouse-driver

运行：
  python clickhouse_query.py

如需自定义查询，请修改 main() 中的示例 SQL。
"""

import sys
from typing import Any, Dict, Optional

# HTTP 客户端（优先）
try:
    import clickhouse_connect  # pip install clickhouse-connect
except Exception:
    clickhouse_connect = None

# TCP 客户端（回退）
try:
    from clickhouse_driver import Client as CHDriverClient  # pip install clickhouse-driver
except Exception:
    CHDriverClient = None


def create_http_client(cfg: Dict[str, Any]):
    if clickhouse_connect is None:
        raise RuntimeError("缺少依赖: clickhouse-connect")
    client = clickhouse_connect.get_client(
        host=cfg.get('host', '127.0.0.1'),
        port=int(cfg.get('http_port', cfg.get('port', 8123))),
        username=cfg.get('username', 'default'),
        password=cfg.get('password', ''),
        database=cfg.get('database', 'default'),
        # 如为 HTTPS，可添加 secure=True / verify=False
        # secure=True,
    )
    return client


def create_tcp_client(cfg: Dict[str, Any]):
    if CHDriverClient is None:
        raise RuntimeError("缺少依赖: clickhouse-driver")
    client = CHDriverClient(
        host=cfg.get('host', '127.0.0.1'),
        port=int(cfg.get('tcp_port', cfg.get('port', 9000))),
        user=cfg.get('username', 'default'),
        password=cfg.get('password', ''),
        database=cfg.get('database', 'default'),
        send_receive_timeout=30,
        connect_timeout=10,
    )
    return client


def run_http_query(client, sql: str, params: Optional[Dict[str, Any]] = None):
    result = client.query(sql, parameters=params or {})
    return result.column_names, result.result_rows


def run_tcp_query(client, sql: str, params: Optional[Dict[str, Any]] = None):
    # clickhouse-driver 使用 %(param)s 格式，需要转换 SQL
    tcp_sql = sql
    if params and '{' in sql:
        # 将 {db:String} 格式转换为 %(db)s 格式
        import re
        tcp_sql = re.sub(r'\{(\w+):[^}]+\}', r'%(\1)s', sql)

    rows = client.execute(tcp_sql, params or {})
    # clickhouse-driver 不会直接返回列名，简单取别名
    cols = []
    return cols, rows


def print_result(sql: str, cols, rows, params: Optional[Dict[str, Any]] = None):
    print(f"\nSQL => {sql}")
    if params:
        print(f"Params => {params}")
    if cols:
        print("Columns:", cols)
    print("Rows:")
    for r in rows:
        print(r)


def main():
    # 配置：你提供的端口 19000 很可能是 TCP 端口，这里可同时给出 http_port 与 tcp_port
    config = {
        'host': '***********',
        'database': 'dataeye',
        'username': 'ssc',
        'password': 'GsvvCs45!B',
        'http_port': 8123,   # 若你们的 HTTP 自定义端口，请改为对应值
        'tcp_port': 19000,   # 题主给出的端口，作为 TCP 回退端口
    }

    # 示例 SQL
    basic_sql = "SELECT version() AS version, now() AS ts"
    tables_sql = (
        "SELECT database, name, engine, total_rows "
        "FROM system.tables WHERE database = {db:String} "
        "ORDER BY name LIMIT 10"
    )

    # 1) 尝试 HTTP
    try:
        http_client = create_http_client(config)
        version_info = http_client.server_version
        print(f"[HTTP] 已连接 ClickHouse，服务器版本: {version_info}")

        cols, rows = run_http_query(http_client, basic_sql)
        print_result(basic_sql, cols, rows)

        cols, rows = run_http_query(http_client, tables_sql, params={"db": config['database']})
        print_result(tables_sql, cols, rows, params={"db": config['database']})
        return
    except Exception as http_err:
        print(f"[HTTP] 连接或查询失败，将回退 TCP。原因: {http_err}")

    # 2) 回退 TCP
    try:
        tcp_client = create_tcp_client(config)
        # 不能直接像 HTTP 一样拿版本，执行 SQL 获取
        cols, rows = run_tcp_query(tcp_client, basic_sql)
        print_result(basic_sql, cols, rows)

        cols, rows = run_tcp_query(tcp_client, tables_sql, params={"db": config['database']})
        print_result(tables_sql, cols, rows, params={"db": config['database']})
    except Exception as tcp_err:
        print(f"[TCP] 连接或查询失败: {tcp_err}")
        sys.exit(1)


if __name__ == '__main__':
    main()

