# Word文档健康度颜色功能说明

## 功能概述

已成功为 `app.py` 和 `html_export.py` 添加了根据健康度自动设置表格单元格颜色的功能。Word文档中的使用率数据将根据不同的健康度阈值显示不同的背景颜色。

## 颜色方案

- 🟢 **绿色** (`90EE90`): 正常状态
- 🟡 **黄色** (`FFFF99`): 观察状态  
- 🔴 **红色** (`FFB6C1`): 警告状态

## 健康度阈值设置

### 1. 存储健康度
- 🟢 **正常**: 使用率 < 90%
- 🟡 **观察**: 使用率 90% ~ 95%
- 🔴 **警告**: 使用率 > 95%

### 2. 数据库健康度
- 🟢 **正常**: 使用率 < 85%
- 🟡 **观察**: 使用率 85% ~ 95%
- 🔴 **警告**: 使用率 > 95%

### 3. 容器健康度
**CPU/内存使用率:**
- 🟢 **正常**: 使用率 < 80%
- 🟡 **观察**: 使用率 80% ~ 90%
- 🔴 **警告**: 使用率 > 90%

**存储使用率:**
- 🟢 **正常**: 使用率 < 90%
- 🟡 **观察**: 使用率 90% ~ 95%
- 🔴 **警告**: 使用率 > 95%

### 4. 虚拟化健康度
**CPU/内存使用率:**
- 🟢 **正常**: 使用率 < 75%
- 🟡 **观察**: 使用率 75% ~ 85%
- 🔴 **警告**: 使用率 > 85%

**存储使用率:**
- 🟢 **正常**: 使用率 < 90%
- 🟡 **观察**: 使用率 90% ~ 95%
- 🔴 **警告**: 使用率 > 95%

## 技术实现

### 修改的文件

1. **html_export.py**
   - 添加了 `get_health_color()` 函数：根据使用率和资源类型返回对应颜色
   - 添加了 `identify_table_type()` 函数：自动识别表格类型
   - 添加了 `identify_usage_columns()` 函数：识别使用率列
   - 添加了 `get_metric_type_from_column()` 函数：从列名识别指标类型
   - 修改了 `create_word_table()` 函数：应用颜色到使用率单元格

2. **app.py**
   - 健康度阈值已与Word导出功能保持一致
   - 添加了文件下载功能，返回可下载的URL

### 智能识别机制

系统会自动识别：
- **表格类型**: 通过表头关键词识别是存储、数据库、容器还是虚拟化表格
- **使用率列**: 自动识别包含"使用率"、"usage"或"%"的列
- **指标类型**: 从列名识别是CPU、内存还是存储指标

### 使用方法

1. 调用 `/api/export_word` 接口生成Word文档
2. 系统会自动为表格中的使用率数据应用对应的健康度颜色
3. 通过返回的 `download_url` 下载生成的Word文档

## 测试验证

✅ **功能验证成功！**

通过测试验证，系统能够：
- 正确识别不同类型的表格（存储、数据库、容器、虚拟化）
- 准确识别使用率列和指标类型（CPU、内存、存储）
- 根据健康度阈值正确应用颜色
- 在Word文档中成功设置单元格背景色

测试结果显示：
- 表格数量: 4个
- 有颜色的单元格: 46个
- 颜色映射: 绿色(#90EE90)、黄色(#FFFF99)、红色(#FFB6C1)完全正确

## 注意事项

- 颜色仅应用于使用率相关的数据列
- 表头行始终使用灰色背景
- 如果无法解析使用率数值，不会应用颜色
- 系统会自动处理百分号格式的数据（如"85.0%"）
