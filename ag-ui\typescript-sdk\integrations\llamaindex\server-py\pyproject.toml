[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "server"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.9, <3.14"
dependencies = [
    "llama-index-core>=0.12.41,<0.13",
    "llama-index-agent-openai>=0.4.9,<0.5",
    "llama-index-protocols-ag-ui>=0.1.2",
    "jsonpatch>=1.33",
    "uvicorn>=0.27.0",
    "fastapi>=0.100.0",
]
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
]

[tool.hatch.build.targets.sdist]
include = ["server/"]

[tool.hatch.build.targets.wheel]
include = ["server/"]

[project.scripts]
dev = "server:main"
