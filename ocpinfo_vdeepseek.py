from flask import Flask, jsonify
import requests
from functools import lru_cache
import time
import logging
from datetime import datetime, timedelta

# ========================
# 配置
# ========================
PROMETHEUS_URL = "http://10.5.200.10:8428/api/v1/query"

# 集群 ID 到名称的映射
CLUSTER_NAME_MAP = {
    "JX-K8S-PROD": "嘉兴K8S生产集群",
    "HSY-K8S-PROD": "后沙峪K8S生产集群",
    "JX-K8S-TEST": "嘉兴K8S测试集群",
    "HSY-TAP-PROD": "后沙峪TAP容器集群",
    # 可继续添加
}

# 默认超时
TIMEOUT = 10

# 缓存时间（秒）
CACHE_TTL = 60

# 创建 Flask 应用
app = Flask(__name__)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ========================
# 工具函数
# ========================
def bytes_to_gb(bytes_value):
    """将字节转换为GB"""
    return round(bytes_value / (1024 ** 3), 2) if bytes_value else 0

def query_prometheus(query):
    """查询 Prometheus 数据"""
    try:
        response = requests.get(PROMETHEUS_URL, params={'query': query}, timeout=TIMEOUT)
        response.raise_for_status()
        data = response.json()
        if data.get("status") == "success":
            return data.get("data", {}).get("result", [])
        else:
            logger.error(f"Prometheus 查询失败 [{query}]: {data.get('error', 'Unknown error')}")
            return []
    except requests.exceptions.RequestException as e:
        logger.error(f"请求 Prometheus 失败 [{query}]: {e}")
        return []
    except Exception as e:
        logger.error(f"处理 Prometheus 响应时出错 [{query}]: {e}")
        return []

# ========================
# 数据获取函数（带缓存）
# ========================
# 使用全局变量存储缓存数据和过期时间
cache_data = None
cache_expiry = None

def get_clusters_data():
    """获取集群数据，带缓存功能"""
    global cache_data, cache_expiry
    
    current_time = datetime.now()
    # 如果缓存未过期，直接返回缓存数据
    if cache_data and cache_expiry and current_time < cache_expiry:
        return cache_data
    
    try:
        # 获取新数据
        data = fetch_data()
        # 更新缓存
        cache_data = data
        cache_expiry = current_time + timedelta(seconds=CACHE_TTL)
        return data
    except Exception as e:
        logger.error(f"获取集群数据失败: {e}")
        # 即使获取新数据失败，也返回旧缓存（如果有）
        if cache_data:
            logger.warning("返回过期的缓存数据")
            return cache_data
        raise

def fetch_data():
    """从 Prometheus 获取集群监控数据"""
    # 存储最终结果
    clusters = []

    # 1. 获取每个集群的节点数和 CPU 核数（总核数）
    cpu_cores_query = '''
        sum by (cluster_id) (
            count without (cpu) (
                node_cpu_seconds_total{job="node-exporter"}
            )
        )
    '''
    node_count_query = '''
        count by (cluster_id) (
            count by (cluster_id, instance) (
                node_cpu_seconds_total{job="node-exporter"}
            )
        )
    '''

    # 2. 获取 CPU 使用率（非 idle 时间）
    cpu_usage_query = '''
        sum by (cluster_id) (
            rate(node_cpu_seconds_total{job="node-exporter", mode!="idle"}[5m])
        )
    '''
    cpu_total_query = '''
        sum by (cluster_id) (
            rate(node_cpu_seconds_total{job="node-exporter"}[5m])
        )
    '''

    # 3. 内存使用
    memory_total_query = '''
        sum by (cluster_id) (
            node_memory_MemTotal_bytes{job="node-exporter"}
        )
    '''
    memory_free_query = '''
        sum by (cluster_id) (
            node_memory_MemAvailable_bytes{job="node-exporter"}
        )
    '''

    # 4. 存储使用（文件系统）
    storage_total_query = '''
        sum by (cluster_id) (
            node_filesystem_size_bytes{job="node-exporter", mountpoint="/", fstype!=""}
        )
    '''
    storage_free_query = '''
        sum by (cluster_id) (
            node_filesystem_avail_bytes{job="node-exporter", mountpoint="/", fstype!=""}
        )
    '''

    # 执行所有查询
    queries = {
        'cpu_cores': cpu_cores_query,
        'node_count': node_count_query,
        'cpu_usage': cpu_usage_query,
        'cpu_total': cpu_total_query,
        'memory_total': memory_total_query,
        'memory_free': memory_free_query,
        'storage_total': storage_total_query,
        'storage_free': storage_free_query
    }
    
    results = {}
    for key, query in queries.items():
        results[key] = query_prometheus(query)
        # 添加短暂延迟避免对Prometheus造成过大压力
        time.sleep(0.1)

    # 构建 cluster_id -> 数据 映射
    def list_to_dict(data_list, key='cluster_id'):
        result = {}
        for item in data_list:
            if 'metric' in item and key in item['metric']:
                cluster_id = item['metric'][key]
                try:
                    result[cluster_id] = float(item['value'][1])
                except (ValueError, IndexError):
                    logger.warning(f"解析数值失败: {item}")
                    result[cluster_id] = 0
        return result

    cpu_cores_map = list_to_dict(results['cpu_cores'])
    node_count_map = list_to_dict(results['node_count'])
    cpu_usage_map = list_to_dict(results['cpu_usage'])
    cpu_total_map = list_to_dict(results['cpu_total'])
    memory_total_map = list_to_dict(results['memory_total'])
    memory_free_map = list_to_dict(results['memory_free'])
    storage_total_map = list_to_dict(results['storage_total'])
    storage_free_map = list_to_dict(results['storage_free'])

    # 所有出现的 cluster_id
    all_cluster_ids = set()
    for d in [cpu_cores_map, node_count_map, memory_total_map, storage_total_map]:
        all_cluster_ids.update(d.keys())

    # 构造返回结果
    for cluster_id in all_cluster_ids:
        try:
            total_cores = cpu_cores_map.get(cluster_id, 0)
            node_count = int(node_count_map.get(cluster_id, 0))
            cpu_used = cpu_usage_map.get(cluster_id, 0)
            cpu_total = cpu_total_map.get(cluster_id, 1)  # 防止除零
            cpu_usage_rate = round((cpu_used / cpu_total) * 100, 2) if cpu_total > 0 else 0
            cpu_used_cores = round(total_cores * cpu_usage_rate / 100, 2)

            memory_total_bytes = memory_total_map.get(cluster_id, 0)
            memory_free_bytes = memory_free_map.get(cluster_id, 0)
            memory_used_bytes = max(0, memory_total_bytes - memory_free_bytes)
            memory_total_gb = bytes_to_gb(memory_total_bytes)
            memory_used_gb = bytes_to_gb(memory_used_bytes)
            memory_usage_rate = round((memory_used_bytes / memory_total_bytes) * 100, 2) if memory_total_bytes > 0 else 0

            storage_total_bytes = storage_total_map.get(cluster_id, 0)
            storage_free_bytes = storage_free_map.get(cluster_id, 0)
            storage_used_bytes = max(0, storage_total_bytes - storage_free_bytes)
            storage_total_gb = bytes_to_gb(storage_total_bytes)
            storage_used_gb = bytes_to_gb(storage_used_bytes)
            storage_usage_rate = round((storage_used_bytes / storage_total_bytes) * 100, 2) if storage_total_bytes > 0 else 0

            cluster_name = CLUSTER_NAME_MAP.get(cluster_id, cluster_id)  # 默认用 ID

            clusters.append({
                "cluster_name": cluster_name,
                "cluster_id": cluster_id,
                "cpu_total_cores": round(total_cores, 2),
                "cpu_used_cores": cpu_used_cores,
                "cpu_usage_rate": cpu_usage_rate,
                "memory_total_gb": memory_total_gb,
                "memory_used_gb": memory_used_gb,
                "memory_usage_rate": memory_usage_rate,
                "storage_total_gb": storage_total_gb,
                "storage_used_gb": storage_used_gb,
                "storage_usage_rate": storage_usage_rate,
                "node_count": node_count,
                "last_updated": datetime.now().isoformat()
            })
        except Exception as e:
            logger.error(f"处理 cluster_id={cluster_id} 时出错: {e}")
            continue

    return clusters

# ========================
# API 路由
# ========================
@app.route('/api/clusters', methods=['GET'])
def get_clusters():
    """获取所有集群的监控数据"""
    try:
        data = get_clusters_data()
        return jsonify({
            "status": "success",
            "data": data,
            "count": len(data)
        }), 200
    except Exception as e:
        logger.error(f"API 错误: {e}")
        return jsonify({
            "status": "error",
            "message": "Internal server error"
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    # 简单检查是否能连接到Prometheus
    try:
        test_query = 'up{job="node-exporter"}'
        result = query_prometheus(test_query)
        prometheus_healthy = len(result) > 0
        return jsonify({
            "status": "healthy",
            "prometheus_connected": prometheus_healthy,
            "timestamp": datetime.now().isoformat()
        }), 200
    except Exception as e:
        return jsonify({
            "status": "degraded",
            "prometheus_connected": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 200

@app.route('/cache-info', methods=['GET'])
def cache_info():
    """缓存信息接口"""
    global cache_expiry
    current_time = datetime.now()
    cache_status = "valid" if cache_expiry and current_time < cache_expiry else "expired"
    return jsonify({
        "cache_status": cache_status,
        "cache_expiry": cache_expiry.isoformat() if cache_expiry else None,
        "current_time": current_time.isoformat()
    }), 200

# ========================
# 主程序入口
# ========================
if __name__ == '__main__':
    logger.info("启动 Prometheus 监控 API 服务...")
    logger.info(f"访问 http://localhost:5000/api/clusters")
    app.run(host='0.0.0.0', port=5000, debug=False)