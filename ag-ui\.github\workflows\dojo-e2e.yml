name: e2e

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  e2e:
    name: E2E Tests
    runs-on: depot-ubuntu-latest-8

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'

    - name: Install pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 10.13.1

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true

    - name: Install uv
      uses: astral-sh/setup-uv@v6

    - name: Setup pnpm cache
      uses: actions/cache@v4
      with:
        path: ~/.local/share/pnpm/store
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Install dependencies
      working-directory: typescript-sdk
      run: pnpm install --frozen-lockfile

    - name: Prepare dojo for e2e
      working-directory: typescript-sdk/apps/dojo
      run: node ./scripts/prep-dojo-everything.js -e2e

    - name: Install e2e dependencies
      working-directory: typescript-sdk/apps/dojo/e2e
      run: |
        pnpm install

    - name: write langgraph env files
      working-directory: typescript-sdk/integrations/langgraph
      env:
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        LANGSMITH_API_KEY: ${{ secrets.LANGSMITH_API_KEY }}
      run: |
        echo "OPENAI_API_KEY=${OPENAI_API_KEY}" > examples/python/.env
        echo "LANGSMITH_API_KEY=${LANGSMITH_API_KEY}" >> examples/python/.env
        echo "OPENAI_API_KEY=${OPENAI_API_KEY}" > examples/typescript/.env
        echo "LANGSMITH_API_KEY=${LANGSMITH_API_KEY}" >> examples/typescript/.env
        echo "OPENAI_API_KEY=${OPENAI_API_KEY}" > python/ag_ui_langgraph/.env
        echo "LANGSMITH_API_KEY=${LANGSMITH_API_KEY}" >> python/ag_ui_langgraph/.env

    - name: Run dojo+agents
      uses: JarvusInnovations/background-action@v1
      env:
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        LANGSMITH_API_KEY: ${{ secrets.LANGSMITH_API_KEY }}
      with:
        run: |
          node ../scripts/run-dojo-everything.js
        working-directory: typescript-sdk/apps/dojo/e2e
        wait-on: |
          http://localhost:9999
          tcp:localhost:8000
          tcp:localhost:8001
          tcp:localhost:8002
          tcp:localhost:8003
          tcp:localhost:8004
          tcp:localhost:8005
          tcp:localhost:8006
          tcp:localhost:8007
          tcp:localhost:8008
          tcp:localhost:8009

    - name: Run tests
      working-directory: typescript-sdk/apps/dojo/e2e
      env:
        BASE_URL: http://localhost:9999
      run: pnpm test

    - name: Upload traces
      if: always() # Uploads artifacts even if tests fail
      uses: actions/upload-artifact@v4
      with:
        name: playwright-traces
        path: typescript-sdk/apps/dojo/e2e/test-results/
        retention-days: 7
