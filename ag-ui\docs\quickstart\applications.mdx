---
title: "Build applications"
description:
  "Build agentic applications utilizing compatible event AG-UI event streams"
---

# Introduction

AG-UI provides a concise, event-driven protocol that lets any agent stream rich,
structured output to any client. It can be used to connect any agentic system to
any client.

A client is defined as any system that can receieve, display, and respond to 
AG-UI events. For more information on existing clients and integrations, see
the [integrations](/integrations) page.

# Automatic Setup
AG-UI provides a CLI tool to automatically create or scaffold a new application with any client and server.

```sh
npx create-ag-ui-app@latest
```

<img
  className="w-full rounded-3xl mx-auto"
  src="https://copilotkit-public-assets.s3.us-east-1.amazonaws.com/docs/ag-ui/quickstart.gif"
/>

