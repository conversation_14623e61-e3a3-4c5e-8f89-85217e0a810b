{"name": "@ag-ui/encoder", "author": "<PERSON> <<EMAIL>>", "version": "0.0.37", "private": false, "publishConfig": {"access": "public"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch", "lint": "eslint \"src/**/*.ts*\"", "clean": "rm -rf dist .turbo node_modules", "test": "jest", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}, "dependencies": {"@ag-ui/core": "workspace:*", "@ag-ui/proto": "workspace:*"}, "devDependencies": {"@types/jest": "^29.5.12", "jest": "^29.7.0", "ts-jest": "^29.1.2", "tsup": "^8.0.2", "typescript": "^5.8.2"}}