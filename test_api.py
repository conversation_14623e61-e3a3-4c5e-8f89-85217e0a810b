import requests
import json

# 测试 /api/generate_report 接口
url = "http://************:5002/api/generate_report"

# 测试数据
test_data = {
    "report_id": "test_report",
    "metric": {
        "rows": [
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.77,
                "time": "2025-09-15 00:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.74,
                "time": "2025-09-15 00:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 00:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.1,
                "time": "2025-09-15 00:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 00:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.34,
                "time": "2025-09-15 00:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 00:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 00:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.67,
                "time": "2025-09-15 00:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 00:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.54,
                "time": "2025-09-15 00:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 00:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 00:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.61,
                "time": "2025-09-15 00:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 00:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.32,
                "time": "2025-09-15 00:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 00:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 00:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.61,
                "time": "2025-09-15 00:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 00:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.2,
                "time": "2025-09-15 00:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 00:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.55,
                "time": "2025-09-15 00:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.04,
                "time": "2025-09-15 00:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.99,
                "time": "2025-09-15 00:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.62,
                "time": "2025-09-15 00:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 00:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.0,
                "time": "2025-09-15 00:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.27,
                "time": "2025-09-15 00:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 00:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.31,
                "time": "2025-09-15 00:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 00:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 00:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.89,
                "time": "2025-09-15 00:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.83,
                "time": "2025-09-15 00:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 00:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.27,
                "time": "2025-09-15 00:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 00:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.82,
                "time": "2025-09-15 00:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.7,
                "time": "2025-09-15 00:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.44,
                "time": "2025-09-15 00:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.97,
                "time": "2025-09-15 00:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 00:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.08,
                "time": "2025-09-15 00:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 00:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.88,
                "time": "2025-09-15 00:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.17,
                "time": "2025-09-15 01:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.48,
                "time": "2025-09-15 01:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 01:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.66,
                "time": "2025-09-15 01:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.56,
                "time": "2025-09-15 01:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 01:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.74,
                "time": "2025-09-15 01:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.56,
                "time": "2025-09-15 01:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.85,
                "time": "2025-09-15 01:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 01:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.88,
                "time": "2025-09-15 01:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.57,
                "time": "2025-09-15 01:16:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.93,
                "time": "2025-09-15 01:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.54,
                "time": "2025-09-15 01:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 01:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.2,
                "time": "2025-09-15 01:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 01:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.88,
                "time": "2025-09-15 01:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 01:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.69,
                "time": "2025-09-15 01:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 01:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.63,
                "time": "2025-09-15 01:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.69,
                "time": "2025-09-15 01:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 01:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.91,
                "time": "2025-09-15 01:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 01:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.28,
                "time": "2025-09-15 01:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 01:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.63,
                "time": "2025-09-15 01:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.65,
                "time": "2025-09-15 01:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.63,
                "time": "2025-09-15 01:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 01:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.45,
                "time": "2025-09-15 01:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.85,
                "time": "2025-09-15 01:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.26,
                "time": "2025-09-15 01:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.06,
                "time": "2025-09-15 01:48:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 01:50:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.96,
                "time": "2025-09-15 01:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.21,
                "time": "2025-09-15 01:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.38,
                "time": "2025-09-15 01:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.29,
                "time": "2025-09-15 01:56:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.17,
                "time": "2025-09-15 01:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 83.68,
                "time": "2025-09-15 02:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 02:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.65,
                "time": "2025-09-15 02:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 02:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.96,
                "time": "2025-09-15 02:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.64,
                "time": "2025-09-15 02:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.17,
                "time": "2025-09-15 02:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.79,
                "time": "2025-09-15 02:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 02:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.89,
                "time": "2025-09-15 02:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 02:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.15,
                "time": "2025-09-15 02:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 02:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.96,
                "time": "2025-09-15 02:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 02:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.04,
                "time": "2025-09-15 02:18:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 02:20:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.56,
                "time": "2025-09-15 02:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 02:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.44,
                "time": "2025-09-15 02:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.67,
                "time": "2025-09-15 02:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.04,
                "time": "2025-09-15 02:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 02:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.65,
                "time": "2025-09-15 02:28:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 02:30:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.02,
                "time": "2025-09-15 02:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 02:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.83,
                "time": "2025-09-15 02:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 02:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.8,
                "time": "2025-09-15 02:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.62,
                "time": "2025-09-15 02:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 02:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.99,
                "time": "2025-09-15 02:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.55,
                "time": "2025-09-15 02:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.4,
                "time": "2025-09-15 02:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.12,
                "time": "2025-09-15 02:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 02:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.42,
                "time": "2025-09-15 02:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 02:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.18,
                "time": "2025-09-15 02:48:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 02:50:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.48,
                "time": "2025-09-15 02:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 02:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.63,
                "time": "2025-09-15 02:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 02:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.42,
                "time": "2025-09-15 02:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 02:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 02:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.51,
                "time": "2025-09-15 03:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.48,
                "time": "2025-09-15 03:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 03:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.49,
                "time": "2025-09-15 03:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.33,
                "time": "2025-09-15 03:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 03:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.14,
                "time": "2025-09-15 03:08:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 03:10:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.3,
                "time": "2025-09-15 03:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.28,
                "time": "2025-09-15 03:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 03:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.2,
                "time": "2025-09-15 03:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 03:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.67,
                "time": "2025-09-15 03:16:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.23,
                "time": "2025-09-15 03:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.25,
                "time": "2025-09-15 03:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 03:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.56,
                "time": "2025-09-15 03:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 03:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.81,
                "time": "2025-09-15 03:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 03:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.67,
                "time": "2025-09-15 03:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.18,
                "time": "2025-09-15 03:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.1,
                "time": "2025-09-15 03:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 03:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.59,
                "time": "2025-09-15 03:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.85,
                "time": "2025-09-15 03:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 03:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.54,
                "time": "2025-09-15 03:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 03:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.38,
                "time": "2025-09-15 03:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.49,
                "time": "2025-09-15 03:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 03:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.44,
                "time": "2025-09-15 03:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.1,
                "time": "2025-09-15 03:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.27,
                "time": "2025-09-15 03:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.29,
                "time": "2025-09-15 03:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.05,
                "time": "2025-09-15 03:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.69,
                "time": "2025-09-15 03:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 03:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.04,
                "time": "2025-09-15 03:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 03:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.07,
                "time": "2025-09-15 03:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 03:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.6,
                "time": "2025-09-15 03:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.34,
                "time": "2025-09-15 04:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 04:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.54,
                "time": "2025-09-15 04:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 04:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.36,
                "time": "2025-09-15 04:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.51,
                "time": "2025-09-15 04:08:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 04:10:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.23,
                "time": "2025-09-15 04:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 04:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.08,
                "time": "2025-09-15 04:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 04:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 04:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.36,
                "time": "2025-09-15 04:16:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.75,
                "time": "2025-09-15 04:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.88,
                "time": "2025-09-15 04:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.96,
                "time": "2025-09-15 04:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 04:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.03,
                "time": "2025-09-15 04:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.53,
                "time": "2025-09-15 04:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 04:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.74,
                "time": "2025-09-15 04:28:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 04:30:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.44,
                "time": "2025-09-15 04:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 04:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 04:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.96,
                "time": "2025-09-15 04:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 04:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 04:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.84,
                "time": "2025-09-15 04:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.4,
                "time": "2025-09-15 04:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 04:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 91.03,
                "time": "2025-09-15 04:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.96,
                "time": "2025-09-15 04:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 04:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.93,
                "time": "2025-09-15 04:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.31,
                "time": "2025-09-15 04:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.97,
                "time": "2025-09-15 04:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 04:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.68,
                "time": "2025-09-15 04:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.06,
                "time": "2025-09-15 04:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.03,
                "time": "2025-09-15 04:56:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.91,
                "time": "2025-09-15 04:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 05:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.21,
                "time": "2025-09-15 05:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.68,
                "time": "2025-09-15 05:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.41,
                "time": "2025-09-15 05:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 05:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.07,
                "time": "2025-09-15 05:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.21,
                "time": "2025-09-15 05:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.84,
                "time": "2025-09-15 05:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 05:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.49,
                "time": "2025-09-15 05:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.67,
                "time": "2025-09-15 05:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 05:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.32,
                "time": "2025-09-15 05:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 05:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 05:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.97,
                "time": "2025-09-15 05:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 05:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.61,
                "time": "2025-09-15 05:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.67,
                "time": "2025-09-15 05:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.93,
                "time": "2025-09-15 05:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 05:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.98,
                "time": "2025-09-15 05:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 05:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 05:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 05:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.92,
                "time": "2025-09-15 05:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 05:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.23,
                "time": "2025-09-15 05:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 05:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 05:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.85,
                "time": "2025-09-15 05:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.37,
                "time": "2025-09-15 05:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 05:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.91,
                "time": "2025-09-15 05:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.17,
                "time": "2025-09-15 05:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.47,
                "time": "2025-09-15 05:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.46,
                "time": "2025-09-15 05:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.85,
                "time": "2025-09-15 05:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 05:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.93,
                "time": "2025-09-15 05:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 05:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.32,
                "time": "2025-09-15 05:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 05:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.18,
                "time": "2025-09-15 05:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.1,
                "time": "2025-09-15 06:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.4,
                "time": "2025-09-15 06:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 06:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.21,
                "time": "2025-09-15 06:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.88,
                "time": "2025-09-15 06:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 06:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.77,
                "time": "2025-09-15 06:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.24,
                "time": "2025-09-15 06:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 06:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.61,
                "time": "2025-09-15 06:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 06:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.26,
                "time": "2025-09-15 06:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 06:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.52,
                "time": "2025-09-15 06:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 06:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.19,
                "time": "2025-09-15 06:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.47,
                "time": "2025-09-15 06:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 06:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 06:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 06:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.74,
                "time": "2025-09-15 06:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 06:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.79,
                "time": "2025-09-15 06:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 06:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.57,
                "time": "2025-09-15 06:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.51,
                "time": "2025-09-15 06:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 06:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 06:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 06:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.59,
                "time": "2025-09-15 06:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.73,
                "time": "2025-09-15 06:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.17,
                "time": "2025-09-15 06:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 06:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.0,
                "time": "2025-09-15 06:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.48,
                "time": "2025-09-15 06:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 06:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.87,
                "time": "2025-09-15 06:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 06:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.16,
                "time": "2025-09-15 06:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 06:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.03,
                "time": "2025-09-15 06:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.82,
                "time": "2025-09-15 06:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 06:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 06:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.76,
                "time": "2025-09-15 06:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 06:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 06:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.07,
                "time": "2025-09-15 07:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 07:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.77,
                "time": "2025-09-15 07:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.09,
                "time": "2025-09-15 07:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 07:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.65,
                "time": "2025-09-15 07:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.08,
                "time": "2025-09-15 07:08:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 07:10:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.6,
                "time": "2025-09-15 07:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 07:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.54,
                "time": "2025-09-15 07:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 07:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.17,
                "time": "2025-09-15 07:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 07:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.62,
                "time": "2025-09-15 07:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 07:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.97,
                "time": "2025-09-15 07:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.36,
                "time": "2025-09-15 07:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.72,
                "time": "2025-09-15 07:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 07:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 07:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.78,
                "time": "2025-09-15 07:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.59,
                "time": "2025-09-15 07:28:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 07:30:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.04,
                "time": "2025-09-15 07:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.41,
                "time": "2025-09-15 07:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 07:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.39,
                "time": "2025-09-15 07:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 07:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.32,
                "time": "2025-09-15 07:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 07:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.28,
                "time": "2025-09-15 07:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 07:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.46,
                "time": "2025-09-15 07:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.54,
                "time": "2025-09-15 07:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 07:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.16,
                "time": "2025-09-15 07:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 07:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 07:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.84,
                "time": "2025-09-15 07:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.93,
                "time": "2025-09-15 07:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.39,
                "time": "2025-09-15 07:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 07:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.18,
                "time": "2025-09-15 07:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.53,
                "time": "2025-09-15 07:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 07:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.91,
                "time": "2025-09-15 07:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 08:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.35,
                "time": "2025-09-15 08:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 08:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 08:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.01,
                "time": "2025-09-15 08:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.22,
                "time": "2025-09-15 08:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 08:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.29,
                "time": "2025-09-15 08:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.15,
                "time": "2025-09-15 08:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.83,
                "time": "2025-09-15 08:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.62,
                "time": "2025-09-15 08:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 08:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.94,
                "time": "2025-09-15 08:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 08:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.67,
                "time": "2025-09-15 08:18:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 08:20:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.76,
                "time": "2025-09-15 08:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 08:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.11,
                "time": "2025-09-15 08:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 08:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.18,
                "time": "2025-09-15 08:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 08:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.31,
                "time": "2025-09-15 08:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 08:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.36,
                "time": "2025-09-15 08:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 08:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 08:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 08:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 08:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 08:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 08:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.96,
                "time": "2025-09-15 08:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 08:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 08:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.42,
                "time": "2025-09-15 08:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.42,
                "time": "2025-09-15 08:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 08:42:02"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 08:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.58,
                "time": "2025-09-15 08:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.58,
                "time": "2025-09-15 08:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.61,
                "time": "2025-09-15 08:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.61,
                "time": "2025-09-15 08:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.66,
                "time": "2025-09-15 08:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.66,
                "time": "2025-09-15 08:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.64,
                "time": "2025-09-15 08:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 08:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.41,
                "time": "2025-09-15 08:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 08:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 08:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.61,
                "time": "2025-09-15 08:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 08:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.0,
                "time": "2025-09-15 08:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 09:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.27,
                "time": "2025-09-15 09:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 09:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.76,
                "time": "2025-09-15 09:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 09:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.69,
                "time": "2025-09-15 09:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 09:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.39,
                "time": "2025-09-15 09:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 09:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.81,
                "time": "2025-09-15 09:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.61,
                "time": "2025-09-15 09:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 09:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 09:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.59,
                "time": "2025-09-15 09:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 09:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 09:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 09:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.51,
                "time": "2025-09-15 09:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.5,
                "time": "2025-09-15 09:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.06,
                "time": "2025-09-15 09:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.25,
                "time": "2025-09-15 09:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 09:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.03,
                "time": "2025-09-15 09:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.72,
                "time": "2025-09-15 09:28:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 09:30:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.41,
                "time": "2025-09-15 09:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.96,
                "time": "2025-09-15 09:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.31,
                "time": "2025-09-15 09:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.01,
                "time": "2025-09-15 09:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.82,
                "time": "2025-09-15 09:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.04,
                "time": "2025-09-15 09:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.42,
                "time": "2025-09-15 09:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.2,
                "time": "2025-09-15 09:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 09:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 09:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 09:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.88,
                "time": "2025-09-15 09:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.13,
                "time": "2025-09-15 09:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.83,
                "time": "2025-09-15 09:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 09:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.37,
                "time": "2025-09-15 09:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 09:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.54,
                "time": "2025-09-15 09:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 09:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.45,
                "time": "2025-09-15 09:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.1,
                "time": "2025-09-15 10:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 10:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 10:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.52,
                "time": "2025-09-15 10:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 10:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.45,
                "time": "2025-09-15 10:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 10:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.42,
                "time": "2025-09-15 10:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 10:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.65,
                "time": "2025-09-15 10:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.46,
                "time": "2025-09-15 10:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 10:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.49,
                "time": "2025-09-15 10:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 10:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.95,
                "time": "2025-09-15 10:18:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 10:20:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.31,
                "time": "2025-09-15 10:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 10:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.91,
                "time": "2025-09-15 10:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 10:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 10:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 10:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.18,
                "time": "2025-09-15 10:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 10:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.53,
                "time": "2025-09-15 10:28:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 10:30:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.95,
                "time": "2025-09-15 10:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 10:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.54,
                "time": "2025-09-15 10:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 10:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.84,
                "time": "2025-09-15 10:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.31,
                "time": "2025-09-15 10:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 10:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.5,
                "time": "2025-09-15 10:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 10:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.38,
                "time": "2025-09-15 10:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 10:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.21,
                "time": "2025-09-15 10:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 10:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.19,
                "time": "2025-09-15 10:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.3,
                "time": "2025-09-15 10:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.3,
                "time": "2025-09-15 10:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.37,
                "time": "2025-09-15 10:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 10:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.75,
                "time": "2025-09-15 10:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.53,
                "time": "2025-09-15 10:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.45,
                "time": "2025-09-15 10:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 10:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.76,
                "time": "2025-09-15 10:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.78,
                "time": "2025-09-15 11:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 11:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.14,
                "time": "2025-09-15 11:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.16,
                "time": "2025-09-15 11:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.64,
                "time": "2025-09-15 11:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.37,
                "time": "2025-09-15 11:08:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 11:10:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.69,
                "time": "2025-09-15 11:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 11:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.91,
                "time": "2025-09-15 11:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 11:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.82,
                "time": "2025-09-15 11:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 11:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.21,
                "time": "2025-09-15 11:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 11:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.98,
                "time": "2025-09-15 11:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.57,
                "time": "2025-09-15 11:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 11:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 11:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.8,
                "time": "2025-09-15 11:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.74,
                "time": "2025-09-15 11:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 11:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.35,
                "time": "2025-09-15 11:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.68,
                "time": "2025-09-15 11:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 11:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.94,
                "time": "2025-09-15 11:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 11:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.56,
                "time": "2025-09-15 11:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 11:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.69,
                "time": "2025-09-15 11:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.27,
                "time": "2025-09-15 11:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 11:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.16,
                "time": "2025-09-15 11:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 11:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 11:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 11:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.56,
                "time": "2025-09-15 11:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 11:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.91,
                "time": "2025-09-15 11:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.32,
                "time": "2025-09-15 11:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.05,
                "time": "2025-09-15 11:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 11:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.41,
                "time": "2025-09-15 11:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.43,
                "time": "2025-09-15 11:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.5,
                "time": "2025-09-15 11:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 11:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 11:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 12:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.1,
                "time": "2025-09-15 12:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.6,
                "time": "2025-09-15 12:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 12:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 12:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 12:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.3,
                "time": "2025-09-15 12:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 12:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 12:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.08,
                "time": "2025-09-15 12:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.86,
                "time": "2025-09-15 12:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 12:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.7,
                "time": "2025-09-15 12:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.47,
                "time": "2025-09-15 12:16:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 12:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.6,
                "time": "2025-09-15 12:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 12:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.27,
                "time": "2025-09-15 12:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.89,
                "time": "2025-09-15 12:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 12:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.38,
                "time": "2025-09-15 12:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.65,
                "time": "2025-09-15 12:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.48,
                "time": "2025-09-15 12:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 12:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.28,
                "time": "2025-09-15 12:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.14,
                "time": "2025-09-15 12:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 12:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.2,
                "time": "2025-09-15 12:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.44,
                "time": "2025-09-15 12:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.85,
                "time": "2025-09-15 12:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 12:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.28,
                "time": "2025-09-15 12:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.25,
                "time": "2025-09-15 12:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 12:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.52,
                "time": "2025-09-15 12:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.42,
                "time": "2025-09-15 12:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 12:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.27,
                "time": "2025-09-15 12:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 12:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.16,
                "time": "2025-09-15 12:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 12:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.53,
                "time": "2025-09-15 12:56:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.63,
                "time": "2025-09-15 12:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.5,
                "time": "2025-09-15 13:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 13:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.6,
                "time": "2025-09-15 13:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 13:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.47,
                "time": "2025-09-15 13:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 13:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 13:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.1,
                "time": "2025-09-15 13:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.1,
                "time": "2025-09-15 13:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.34,
                "time": "2025-09-15 13:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.34,
                "time": "2025-09-15 13:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 13:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 13:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 13:14:02"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 13:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.34,
                "time": "2025-09-15 13:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.34,
                "time": "2025-09-15 13:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.72,
                "time": "2025-09-15 13:16:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.72,
                "time": "2025-09-15 13:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 13:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.48,
                "time": "2025-09-15 13:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.47,
                "time": "2025-09-15 13:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.76,
                "time": "2025-09-15 13:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 13:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.57,
                "time": "2025-09-15 13:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.5,
                "time": "2025-09-15 13:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 13:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.08,
                "time": "2025-09-15 13:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.09,
                "time": "2025-09-15 13:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 13:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.74,
                "time": "2025-09-15 13:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 13:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.37,
                "time": "2025-09-15 13:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.59,
                "time": "2025-09-15 13:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 13:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.09,
                "time": "2025-09-15 13:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 13:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.86,
                "time": "2025-09-15 13:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 13:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.47,
                "time": "2025-09-15 13:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 13:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.74,
                "time": "2025-09-15 13:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 13:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 13:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 13:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 13:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.03,
                "time": "2025-09-15 13:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.52,
                "time": "2025-09-15 13:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 13:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 13:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.99,
                "time": "2025-09-15 13:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 13:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.29,
                "time": "2025-09-15 13:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 14:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.77,
                "time": "2025-09-15 14:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.02,
                "time": "2025-09-15 14:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.58,
                "time": "2025-09-15 14:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 14:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.42,
                "time": "2025-09-15 14:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.56,
                "time": "2025-09-15 14:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.68,
                "time": "2025-09-15 14:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.61,
                "time": "2025-09-15 14:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.27,
                "time": "2025-09-15 14:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.61,
                "time": "2025-09-15 14:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 14:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 14:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.99,
                "time": "2025-09-15 14:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 14:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.68,
                "time": "2025-09-15 14:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.61,
                "time": "2025-09-15 14:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.65,
                "time": "2025-09-15 14:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.92,
                "time": "2025-09-15 14:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 14:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.61,
                "time": "2025-09-15 14:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 14:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.52,
                "time": "2025-09-15 14:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 14:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.71,
                "time": "2025-09-15 14:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.02,
                "time": "2025-09-15 14:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 14:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.39,
                "time": "2025-09-15 14:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.77,
                "time": "2025-09-15 14:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.36,
                "time": "2025-09-15 14:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.09,
                "time": "2025-09-15 14:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.4,
                "time": "2025-09-15 14:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.94,
                "time": "2025-09-15 14:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.16,
                "time": "2025-09-15 14:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.46,
                "time": "2025-09-15 14:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 14:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.64,
                "time": "2025-09-15 14:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 14:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.8,
                "time": "2025-09-15 14:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.33,
                "time": "2025-09-15 14:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 14:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.36,
                "time": "2025-09-15 14:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 14:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.79,
                "time": "2025-09-15 14:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 15:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.8,
                "time": "2025-09-15 15:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 15:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.64,
                "time": "2025-09-15 15:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 15:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.35,
                "time": "2025-09-15 15:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.08,
                "time": "2025-09-15 15:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.02,
                "time": "2025-09-15 15:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.91,
                "time": "2025-09-15 15:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.31,
                "time": "2025-09-15 15:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.3,
                "time": "2025-09-15 15:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 15:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.02,
                "time": "2025-09-15 15:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 15:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.71,
                "time": "2025-09-15 15:18:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 15:20:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 15:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.69,
                "time": "2025-09-15 15:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 15:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.52,
                "time": "2025-09-15 15:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.05,
                "time": "2025-09-15 15:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 15:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.57,
                "time": "2025-09-15 15:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.03,
                "time": "2025-09-15 15:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.74,
                "time": "2025-09-15 15:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 15:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.14,
                "time": "2025-09-15 15:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 15:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.2,
                "time": "2025-09-15 15:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 15:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.77,
                "time": "2025-09-15 15:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.69,
                "time": "2025-09-15 15:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.64,
                "time": "2025-09-15 15:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.02,
                "time": "2025-09-15 15:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.3,
                "time": "2025-09-15 15:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 15:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.64,
                "time": "2025-09-15 15:48:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 15:50:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.18,
                "time": "2025-09-15 15:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 15:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 15:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 15:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.86,
                "time": "2025-09-15 15:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.93,
                "time": "2025-09-15 15:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 15:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.09,
                "time": "2025-09-15 15:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 16:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.86,
                "time": "2025-09-15 16:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 16:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.08,
                "time": "2025-09-15 16:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 16:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.74,
                "time": "2025-09-15 16:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 16:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.94,
                "time": "2025-09-15 16:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 16:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.82,
                "time": "2025-09-15 16:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.24,
                "time": "2025-09-15 16:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 16:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.08,
                "time": "2025-09-15 16:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 86.91,
                "time": "2025-09-15 16:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.66,
                "time": "2025-09-15 16:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.66,
                "time": "2025-09-15 16:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.35,
                "time": "2025-09-15 16:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.0,
                "time": "2025-09-15 16:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 16:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.53,
                "time": "2025-09-15 16:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 16:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 16:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 16:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.66,
                "time": "2025-09-15 16:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.38,
                "time": "2025-09-15 16:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.42,
                "time": "2025-09-15 16:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.4,
                "time": "2025-09-15 16:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 16:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 16:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.7,
                "time": "2025-09-15 16:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.78,
                "time": "2025-09-15 16:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 16:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 16:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 16:40:02"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 16:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.12,
                "time": "2025-09-15 16:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.12,
                "time": "2025-09-15 16:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 16:42:02"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 16:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.09,
                "time": "2025-09-15 16:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 16:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.5,
                "time": "2025-09-15 16:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 16:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.04,
                "time": "2025-09-15 16:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.17,
                "time": "2025-09-15 16:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.34,
                "time": "2025-09-15 16:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 16:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.05,
                "time": "2025-09-15 16:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 16:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 16:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.15,
                "time": "2025-09-15 16:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 16:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 16:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.75,
                "time": "2025-09-15 17:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 17:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 17:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.69,
                "time": "2025-09-15 17:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 17:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.39,
                "time": "2025-09-15 17:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 17:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 17:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.45,
                "time": "2025-09-15 17:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.5,
                "time": "2025-09-15 17:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 17:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.18,
                "time": "2025-09-15 17:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.49,
                "time": "2025-09-15 17:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.65,
                "time": "2025-09-15 17:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.96,
                "time": "2025-09-15 17:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.2,
                "time": "2025-09-15 17:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.05,
                "time": "2025-09-15 17:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 17:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.35,
                "time": "2025-09-15 17:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.81,
                "time": "2025-09-15 17:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.22,
                "time": "2025-09-15 17:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 17:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 17:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.69,
                "time": "2025-09-15 17:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.42,
                "time": "2025-09-15 17:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 17:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.97,
                "time": "2025-09-15 17:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 17:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.7,
                "time": "2025-09-15 17:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 17:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.29,
                "time": "2025-09-15 17:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 17:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.0,
                "time": "2025-09-15 17:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 17:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.81,
                "time": "2025-09-15 17:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.39,
                "time": "2025-09-15 17:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.2,
                "time": "2025-09-15 17:48:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 17:50:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.01,
                "time": "2025-09-15 17:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 17:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.19,
                "time": "2025-09-15 17:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.61,
                "time": "2025-09-15 17:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.35,
                "time": "2025-09-15 17:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 17:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 17:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 17:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.47,
                "time": "2025-09-15 17:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.82,
                "time": "2025-09-15 18:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 18:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.0,
                "time": "2025-09-15 18:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 18:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.35,
                "time": "2025-09-15 18:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.82,
                "time": "2025-09-15 18:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 18:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.81,
                "time": "2025-09-15 18:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 18:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.07,
                "time": "2025-09-15 18:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.18,
                "time": "2025-09-15 18:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 18:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.26,
                "time": "2025-09-15 18:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 18:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 18:18:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 18:20:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.93,
                "time": "2025-09-15 18:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 18:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.75,
                "time": "2025-09-15 18:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.68,
                "time": "2025-09-15 18:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.76,
                "time": "2025-09-15 18:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 18:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.07,
                "time": "2025-09-15 18:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.68,
                "time": "2025-09-15 18:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 18:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.05,
                "time": "2025-09-15 18:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.61,
                "time": "2025-09-15 18:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.84,
                "time": "2025-09-15 18:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.42,
                "time": "2025-09-15 18:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 18:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.91,
                "time": "2025-09-15 18:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.06,
                "time": "2025-09-15 18:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.1,
                "time": "2025-09-15 18:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.46,
                "time": "2025-09-15 18:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 18:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.13,
                "time": "2025-09-15 18:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.73,
                "time": "2025-09-15 18:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 18:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.43,
                "time": "2025-09-15 18:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 18:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.42,
                "time": "2025-09-15 18:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 18:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.05,
                "time": "2025-09-15 18:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.61,
                "time": "2025-09-15 18:56:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.35,
                "time": "2025-09-15 18:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.86,
                "time": "2025-09-15 19:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 19:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.65,
                "time": "2025-09-15 19:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 19:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.65,
                "time": "2025-09-15 19:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 19:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 19:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 19:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.5,
                "time": "2025-09-15 19:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.34,
                "time": "2025-09-15 19:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.44,
                "time": "2025-09-15 19:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 19:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.34,
                "time": "2025-09-15 19:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.78,
                "time": "2025-09-15 19:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 19:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.6,
                "time": "2025-09-15 19:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 19:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.41,
                "time": "2025-09-15 19:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 19:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 19:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.85,
                "time": "2025-09-15 19:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 19:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.97,
                "time": "2025-09-15 19:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.1,
                "time": "2025-09-15 19:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.86,
                "time": "2025-09-15 19:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.14,
                "time": "2025-09-15 19:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 19:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 19:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 19:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.67,
                "time": "2025-09-15 19:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 19:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.8,
                "time": "2025-09-15 19:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 19:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.76,
                "time": "2025-09-15 19:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.86,
                "time": "2025-09-15 19:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.72,
                "time": "2025-09-15 19:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.83,
                "time": "2025-09-15 19:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.38,
                "time": "2025-09-15 19:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 19:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.54,
                "time": "2025-09-15 19:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.87,
                "time": "2025-09-15 19:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 19:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.59,
                "time": "2025-09-15 19:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.0,
                "time": "2025-09-15 20:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.4,
                "time": "2025-09-15 20:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 20:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.27,
                "time": "2025-09-15 20:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.97,
                "time": "2025-09-15 20:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.57,
                "time": "2025-09-15 20:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.51,
                "time": "2025-09-15 20:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.45,
                "time": "2025-09-15 20:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 20:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.48,
                "time": "2025-09-15 20:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 20:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.37,
                "time": "2025-09-15 20:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 20:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.7,
                "time": "2025-09-15 20:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.28,
                "time": "2025-09-15 20:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 20:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.55,
                "time": "2025-09-15 20:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 91.1,
                "time": "2025-09-15 20:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 20:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.1,
                "time": "2025-09-15 20:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 20:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.38,
                "time": "2025-09-15 20:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.38,
                "time": "2025-09-15 20:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 20:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.71,
                "time": "2025-09-15 20:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 20:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.88,
                "time": "2025-09-15 20:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.23,
                "time": "2025-09-15 20:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 20:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.13,
                "time": "2025-09-15 20:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.37,
                "time": "2025-09-15 20:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 20:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.49,
                "time": "2025-09-15 20:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.54,
                "time": "2025-09-15 20:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.29,
                "time": "2025-09-15 20:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.95,
                "time": "2025-09-15 20:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.23,
                "time": "2025-09-15 20:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.09,
                "time": "2025-09-15 20:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 91.02,
                "time": "2025-09-15 20:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 20:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 20:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 20:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.54,
                "time": "2025-09-15 20:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 21:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 21:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 21:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 21:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 21:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.55,
                "time": "2025-09-15 21:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 21:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.81,
                "time": "2025-09-15 21:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 21:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.43,
                "time": "2025-09-15 21:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.4,
                "time": "2025-09-15 21:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.4,
                "time": "2025-09-15 21:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.96,
                "time": "2025-09-15 21:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 21:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.09,
                "time": "2025-09-15 21:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 21:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 21:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.45,
                "time": "2025-09-15 21:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 21:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.1,
                "time": "2025-09-15 21:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 21:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.29,
                "time": "2025-09-15 21:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 21:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.35,
                "time": "2025-09-15 21:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.22,
                "time": "2025-09-15 21:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.48,
                "time": "2025-09-15 21:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 21:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.42,
                "time": "2025-09-15 21:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 21:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.41,
                "time": "2025-09-15 21:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.72,
                "time": "2025-09-15 21:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.14,
                "time": "2025-09-15 21:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.59,
                "time": "2025-09-15 21:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 21:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.64,
                "time": "2025-09-15 21:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 21:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.43,
                "time": "2025-09-15 21:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.3,
                "time": "2025-09-15 21:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 21:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 21:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.63,
                "time": "2025-09-15 21:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 21:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.18,
                "time": "2025-09-15 21:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 21:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.49,
                "time": "2025-09-15 21:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 21:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.27,
                "time": "2025-09-15 21:56:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.07,
                "time": "2025-09-15 21:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.96,
                "time": "2025-09-15 22:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 22:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.0,
                "time": "2025-09-15 22:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 22:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.9,
                "time": "2025-09-15 22:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.44,
                "time": "2025-09-15 22:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.51,
                "time": "2025-09-15 22:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.46,
                "time": "2025-09-15 22:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.32,
                "time": "2025-09-15 22:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.73,
                "time": "2025-09-15 22:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.81,
                "time": "2025-09-15 22:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 22:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.3,
                "time": "2025-09-15 22:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.99,
                "time": "2025-09-15 22:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 22:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.83,
                "time": "2025-09-15 22:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 91.13,
                "time": "2025-09-15 22:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 22:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.59,
                "time": "2025-09-15 22:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.77,
                "time": "2025-09-15 22:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.32,
                "time": "2025-09-15 22:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 22:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.3,
                "time": "2025-09-15 22:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 22:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.2,
                "time": "2025-09-15 22:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 22:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.4,
                "time": "2025-09-15 22:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 22:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.8,
                "time": "2025-09-15 22:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 22:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.56,
                "time": "2025-09-15 22:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 22:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.72,
                "time": "2025-09-15 22:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 22:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.75,
                "time": "2025-09-15 22:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.22,
                "time": "2025-09-15 22:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.91,
                "time": "2025-09-15 22:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 22:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.88,
                "time": "2025-09-15 22:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 22:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.71,
                "time": "2025-09-15 22:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 22:56:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 22:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.82,
                "time": "2025-09-15 23:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 23:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.62,
                "time": "2025-09-15 23:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 23:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.6,
                "time": "2025-09-15 23:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.78,
                "time": "2025-09-15 23:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 23:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.49,
                "time": "2025-09-15 23:08:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.66,
                "time": "2025-09-15 23:10:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.4,
                "time": "2025-09-15 23:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.28,
                "time": "2025-09-15 23:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 23:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.26,
                "time": "2025-09-15 23:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 23:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.74,
                "time": "2025-09-15 23:16:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.91,
                "time": "2025-09-15 23:18:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 23:20:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.79,
                "time": "2025-09-15 23:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 23:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.21,
                "time": "2025-09-15 23:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.0,
                "time": "2025-09-15 23:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 23:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 23:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.25,
                "time": "2025-09-15 23:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.43,
                "time": "2025-09-15 23:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 23:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.46,
                "time": "2025-09-15 23:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.95,
                "time": "2025-09-15 23:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 23:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.76,
                "time": "2025-09-15 23:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 23:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.29,
                "time": "2025-09-15 23:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 23:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.42,
                "time": "2025-09-15 23:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 23:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.14,
                "time": "2025-09-15 23:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.71,
                "time": "2025-09-15 23:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 23:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.85,
                "time": "2025-09-15 23:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.76,
                "time": "2025-09-15 23:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 23:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.94,
                "time": "2025-09-15 23:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.58,
                "time": "2025-09-15 23:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.6,
                "time": "2025-09-15 23:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 23:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.89,
                "time": "2025-09-15 23:58:15"
            }
        ],
        "row_count": 1108
        }
}

try:
    print(f"正在测试接口: {url}")
    print(f"请求方法: POST")
    print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    
    # 发送POST请求
    response = requests.post(url, json=test_data, timeout=10)
    
    print(f"\n响应状态码: {response.status_code}")
    print(f"响应头: {dict(response.headers)}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        print("✅ 接口调用成功!")
    else:
        print(f"❌ 接口调用失败，状态码: {response.status_code}")
        
except requests.exceptions.ConnectionError as e:
    print(f"❌ 连接错误: {e}")
    print("请检查Flask应用是否正在运行在 http://************:5002")
except requests.exceptions.Timeout as e:
    print(f"❌ 请求超时: {e}")
except Exception as e:
    print(f"❌ 其他错误: {e}")

# 测试健康检查接口
print("\n" + "="*50)
print("测试健康检查接口")
try:
    health_url = "http://************:5002/api/health"
    response = requests.get(health_url, timeout=5)
    print(f"健康检查状态码: {response.status_code}")
    print(f"健康检查响应: {response.text}")
except Exception as e:
    print(f"健康检查失败: {e}")
