#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
from psycopg2 import sql
import logging
import json
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import requests
from flask import Flask, jsonify
from functools import lru_cache
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ========================
# storage_info.py 相关功能
# ========================
class EDBConnector:
    """EDB数据库连接器"""
    
    def __init__(self, host: str, port: int, database: str, user: str, password: str):
        self.host = host
        self.port = port
        self.database = database
        self.user = user
        self.password = password
        self.connection = None
        self.cursor = None
    
    def connect(self) -> bool:
        try:
            self.connection = psycopg2.connect(
                host=self.host,
                port=self.port,
                database=self.database,
                user=self.user,
                password=self.password
            )
            self.cursor = self.connection.cursor()
            logger.info(f"成功连接到EDB数据库: {self.database}")
            return True
        except Exception as e:
            logger.error(f"连接数据库失败: {e}")
            return False
    
    def disconnect(self):
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接时出错: {e}")
    
    def execute_query_to_json(self, query: str, params: Optional[tuple] = None) -> str:
        """
        执行查询并将结果转换为JSON格式
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            JSON字符串，格式为: [{"列名1": "值1", "列名2": "值2"}, ...]
        """
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            # 获取列名
            column_names = [desc[0] for desc in self.cursor.description]
            
            # 获取所有行数据
            rows = self.cursor.fetchall()
            
            # 转换为字典列表
            result_list = []
            for row in rows:
                # 处理每一行数据，将None转换为null，datetime等特殊类型转换为字符串
                row_dict = {}
                for i, value in enumerate(row):
                    column_name = column_names[i]
                    # 处理特殊数据类型
                    if value is None:
                        row_dict[column_name] = None
                    elif isinstance(value, datetime):
                        row_dict[column_name] = value.isoformat()
                    elif isinstance(value, (int, float, bool, str)):
                        row_dict[column_name] = value
                    else:
                        # 其他类型转换为字符串
                        row_dict[column_name] = str(value)
                
                result_list.append(row_dict)
            
            # 转换为JSON字符串
            json_result = json.dumps(result_list, ensure_ascii=False, indent=2)
            logger.info(f"查询执行成功，返回 {len(result_list)} 条记录")
            
            return json_result
            
        except Exception as e:
            logger.error(f"执行查询失败: {e}")
            # 返回错误信息的JSON
            error_result = [{"error": str(e)}]
            return json.dumps(error_result, ensure_ascii=False, indent=2)
    
    def execute_single_query_to_json(self, query: str, params: Optional[tuple] = None) -> str:
        """
        执行单行查询并将结果转换为JSON格式
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            JSON字符串，格式为: {"列名1": "值1", "列名2": "值2"}
        """
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            # 获取列名
            column_names = [desc[0] for desc in self.cursor.description]
            
            # 获取单行数据
            row = self.cursor.fetchone()
            
            if row is None:
                # 没有数据时返回空对象
                return json.dumps({}, ensure_ascii=False, indent=2)
            
            # 转换为字典
            row_dict = {}
            for i, value in enumerate(row):
                column_name = column_names[i]
                # 处理特殊数据类型
                if value is None:
                    row_dict[column_name] = None
                elif isinstance(value, datetime):
                    row_dict[column_name] = value.isoformat()
                elif isinstance(value, (int, float, bool, str)):
                    row_dict[column_name] = value
                else:
                    row_dict[column_name] = str(value)
            
            # 转换为JSON字符串
            json_result = json.dumps(row_dict, ensure_ascii=False, indent=2)
            logger.info("单行查询执行成功")
            
            return json_result
            
        except Exception as e:
            logger.error(f"执行单行查询失败: {e}")
            error_result = {"error": str(e)}
            return json.dumps(error_result, ensure_ascii=False, indent=2)

# ========================
# ocpinfo_vdeepseek.py 相关功能
# ========================
PROMETHEUS_URL = "http://10.5.200.10:8428/api/v1/query"

# 集群 ID 到名称的映射
CLUSTER_NAME_MAP = {
    "HSY-M-DB-Prod": "后沙峪中端数据库存储池",
    "HSY-M-VM-Prod": "后沙峪中端虚拟化存储池",
    "JX-M-DB-Prod": "嘉兴中端数据库存储池",
    "JX-M-VM-Prod": "嘉兴中端虚拟化存储池",
    "HSY-H-Prod": "后沙峪高端存储池",
    # 可继续添加
}

# 默认超时
TIMEOUT = 10

# 缓存时间（秒）
CACHE_TTL = 60

# 全局变量存储缓存数据和过期时间
cache_data = None
cache_expiry = None

def bytes_to_gb(bytes_value):
    """将字节转换为GB"""
    return round(bytes_value / (1024 ** 3), 2) if bytes_value else 0

def query_prometheus(query):
    """查询 Prometheus 数据"""
    try:
        response = requests.get(PROMETHEUS_URL, params={'query': query}, timeout=TIMEOUT)
        response.raise_for_status()
        data = response.json()
        if data.get("status") == "success":
            return data.get("data", {}).get("result", [])
        else:
            logger.error(f"Prometheus 查询失败 [{query}]: {data.get('error', 'Unknown error')}")
            return []
    except requests.exceptions.RequestException as e:
        logger.error(f"请求 Prometheus 失败 [{query}]: {e}")
        return []
    except Exception as e:
        logger.error(f"处理 Prometheus 响应时出错 [{query}]: {e}")
        return []

def get_clusters_data():
    """获取集群数据，带缓存功能"""
    global cache_data, cache_expiry
    
    current_time = datetime.now()
    # 如果缓存未过期，直接返回缓存数据
    if cache_data and cache_expiry and current_time < cache_expiry:
        return cache_data
    
    try:
        # 获取新数据
        data = fetch_data()
        # 更新缓存
        cache_data = data
        cache_expiry = current_time + timedelta(seconds=CACHE_TTL)
        return data
    except Exception as e:
        logger.error(f"获取集群数据失败: {e}")
        # 即使获取新数据失败，也返回旧缓存（如果有）
        if cache_data:
            logger.warning("返回过期的缓存数据")
            return cache_data
        raise

def fetch_data():
    """从 Prometheus 获取集群监控数据"""
    # 存储最终结果
    clusters = []

    # 1. 获取每个集群的节点数和 CPU 核数（总核数）
    cpu_cores_query = '''
        sum by (cluster_id) (
            count without (cpu) (
                node_cpu_seconds_total{job="node-exporter"}
            )
        )
    '''
    node_count_query = '''
        count by (cluster_id) (
            count by (cluster_id, instance) (
                node_cpu_seconds_total{job="node-exporter"}
            )
        )
    '''

    # 2. 获取 Pod 数量（运行中的 Pod）
    pod_count_query = '''
        sum by (cluster_id) (
            kube_pod_status_phase{phase="Running"}
        )
    '''

    # 3. 获取 CPU 使用率（非 idle 时间）
    cpu_usage_query = '''
        sum by (cluster_id) (
            rate(node_cpu_seconds_total{job="node-exporter", mode!="idle"}[5m])
        )
    '''
    cpu_total_query = '''
        sum by (cluster_id) (
            rate(node_cpu_seconds_total{job="node-exporter"}[5m])
        )
    '''

    # 4. 内存使用
    memory_total_query = '''
        sum by (cluster_id) (
            node_memory_MemTotal_bytes{job="node-exporter"}
        )
    '''
    memory_free_query = '''
        sum by (cluster_id) (
            node_memory_MemAvailable_bytes{job="node-exporter"}
        )
    '''

    # 5. 存储使用（文件系统）
    storage_total_query = '''
        sum by (cluster_id) (
            node_filesystem_size_bytes{job="node-exporter", mountpoint="/", fstype!=""}
        )
    '''
    storage_free_query = '''
        sum by (cluster_id) (
            node_filesystem_avail_bytes{job="node-exporter", mountpoint="/", fstype!=""}
        )
    '''

    # 执行所有查询
    queries = {
        'cpu_cores': cpu_cores_query,
        'node_count': node_count_query,
        'pod_count': pod_count_query,
        'cpu_usage': cpu_usage_query,
        'cpu_total': cpu_total_query,
        'memory_total': memory_total_query,
        'memory_free': memory_free_query,
        'storage_total': storage_total_query,
        'storage_free': storage_free_query
    }
    
    results = {}
    for key, query in queries.items():
        results[key] = query_prometheus(query)
        # 添加短暂延迟避免对Prometheus造成过大压力
        time.sleep(0.1)

    # 构建 cluster_id -> 数据 映射
    def list_to_dict(data_list, key='cluster_id'):
        result = {}
        for item in data_list:
            if 'metric' in item and key in item['metric']:
                cluster_id = item['metric'][key]
                try:
                    result[cluster_id] = float(item['value'][1])
                except (ValueError, IndexError):
                    logger.warning(f"解析数值失败: {item}")
                    result[cluster_id] = 0
        return result

    cpu_cores_map = list_to_dict(results['cpu_cores'])
    node_count_map = list_to_dict(results['node_count'])
    pod_count_map = list_to_dict(results['pod_count'])
    cpu_usage_map = list_to_dict(results['cpu_usage'])
    cpu_total_map = list_to_dict(results['cpu_total'])
    memory_total_map = list_to_dict(results['memory_total'])
    memory_free_map = list_to_dict(results['memory_free'])
    storage_total_map = list_to_dict(results['storage_total'])
    storage_free_map = list_to_dict(results['storage_free'])

    # 所有出现的 cluster_id
    all_cluster_ids = set()
    for d in [cpu_cores_map, node_count_map, pod_count_map, memory_total_map, storage_total_map]:
        all_cluster_ids.update(d.keys())

    # 构造返回结果
    for cluster_id in all_cluster_ids:
        try:
            total_cores = cpu_cores_map.get(cluster_id, 0)
            node_count = int(node_count_map.get(cluster_id, 0))
            pod_count = int(pod_count_map.get(cluster_id, 0))
            cpu_used = cpu_usage_map.get(cluster_id, 0)
            cpu_total = cpu_total_map.get(cluster_id, 1)  # 防止除零
            cpu_usage_rate = round((cpu_used / cpu_total) * 100, 2) if cpu_total > 0 else 0
            cpu_used_cores = round(total_cores * cpu_usage_rate / 100, 2)

            memory_total_bytes = memory_total_map.get(cluster_id, 0)
            memory_free_bytes = memory_free_map.get(cluster_id, 0)
            memory_used_bytes = max(0, memory_total_bytes - memory_free_bytes)
            memory_total_gb = bytes_to_gb(memory_total_bytes)
            memory_used_gb = bytes_to_gb(memory_used_bytes)
            memory_usage_rate = round((memory_used_bytes / memory_total_bytes) * 100, 2) if memory_total_bytes > 0 else 0

            storage_total_bytes = storage_total_map.get(cluster_id, 0)
            storage_free_bytes = storage_free_map.get(cluster_id, 0)
            storage_used_bytes = max(0, storage_total_bytes - storage_free_bytes)
            storage_total_gb = bytes_to_gb(storage_total_bytes)
            storage_used_gb = bytes_to_gb(storage_used_bytes)
            storage_usage_rate = round((storage_used_bytes / storage_total_bytes) * 100, 2) if storage_total_bytes > 0 else 0

            cluster_name = CLUSTER_NAME_MAP.get(cluster_id, cluster_id)  # 默认用 ID

            clusters.append({
                "cluster_name": cluster_name,
                "cluster_id": cluster_id,
                "node_count": node_count,
                "pod_count": pod_count,
                "cpu_total_cores": round(total_cores, 2),
                "cpu_used_cores": cpu_used_cores,
                "cpu_usage_rate": cpu_usage_rate,
                "memory_total_gb": memory_total_gb,
                "memory_used_gb": memory_used_gb,
                "memory_usage_rate": memory_usage_rate,
                "storage_total_gb": storage_total_gb,
                "storage_used_gb": storage_used_gb,
                "storage_usage_rate": storage_usage_rate,
                "last_updated": datetime.now().isoformat()
            })
        except Exception as e:
            logger.error(f"处理 cluster_id={cluster_id} 时出错: {e}")
            continue

    return clusters

# 创建 Flask 应用
app = Flask(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': '*************',
    'port': 5444,
    'database': 'cloud',
    'user': 'cloud',
    'password': 'Cl123456'
}

# ========================
# API 路由
# ========================
@app.route('/api/storage/ratio', methods=['GET'])
def get_storage_ratio():
    """获取存储资源使用率接口"""
    db = EDBConnector(**DB_CONFIG)
    try:
        if not db.connect():
            return jsonify({"error": "数据库连接失败"}), 500
        
        json_result = db.execute_query_to_json(
            """SELECT resource_pool, sum(total) as total_capacity, sum(used) as used_capacity
            FROM cloud.storage_detail_chart where type = '生产' and resource_pool in ('HSY-M-DB-Prod','HSY-M-VM-Prod','JX-M-VM-Prod','JX-M-DB-Prod','HSY-H-Prod') group by resource_pool
            """
        )
        # 将JSON字符串转换为字典
        result_dict = json.loads(json_result)
        for r in result_dict:
            r['name'] = CLUSTER_NAME_MAP.get(r['resource_pool'])
        return jsonify(result_dict)
        
    except Exception as e:
        logger.error(f"API执行过程中出错: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        db.disconnect()

@app.route('/api/vc/ratio', methods=['GET'])
def get_vc_ratio():
    """获取虚拟化资源使用率接口"""
    db = EDBConnector(**DB_CONFIG)
    try:
        if not db.connect():
            return jsonify({"error": "数据库连接失败"}), 500
        
        json_result = db.execute_query_to_json(
            """SELECT vcname, sum(host_num) as host_num, sum(vm_num) as vm_num, sum(assigned_cpu_num) as cpu_total,
                sum(assigned_cpu_num*cpu_usage*0.01) as cpu_used, sum(total_memory) as memory_total, sum(total_used_memory) as memory_used
                FROM cluster_view
                where vcname like '%生产%' and vcname not like '%非关基%'
                group by vcname """
        )
        # 将JSON字符串转换为字典
        result_dict = json.loads(json_result)
        return jsonify(result_dict)
        
    except Exception as e:
        logger.error(f"API执行过程中出错: {e}")
        return jsonify({"error": str(e)}), 500
    finally:
        db.disconnect()

@app.route('/api/clusters', methods=['GET'])
def get_clusters():
    """获取所有容器集群的监控数据"""
    try:
        data = get_clusters_data()
        return jsonify({
            "status": "success",
            "data": data,
            "count": len(data)
        }), 200
    except Exception as e:
        logger.error(f"API 错误: {e}")
        return jsonify({
            "status": "error",
            "message": "Internal server error"
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    # 简单检查是否能连接到Prometheus
    try:
        test_query = 'up{job="node-exporter"}'
        result = query_prometheus(test_query)
        prometheus_healthy = len(result) > 0
        return jsonify({
            "status": "healthy",
            "prometheus_connected": prometheus_healthy,
            "timestamp": datetime.now().isoformat()
        }), 200
    except Exception as e:
        return jsonify({
            "status": "degraded",
            "prometheus_connected": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 200

@app.route('/cache-info', methods=['GET'])
def cache_info():
    """缓存信息接口"""
    global cache_expiry
    current_time = datetime.now()
    cache_status = "valid" if cache_expiry and current_time < cache_expiry else "expired"
    return jsonify({
        "cache_status": cache_status,
        "cache_expiry": cache_expiry.isoformat() if cache_expiry else None,
        "current_time": current_time.isoformat()
    }), 200

# 运行Flask应用
if __name__ == '__main__':
    logger.info("启动融合后的监控 API 服务...")
    logger.info(f"访问 http://localhost:5001/api/storage/ratio 获取存储资源使用率")
    logger.info(f"访问 http://localhost:5001/api/vc/ratio 获取虚拟化资源使用率")
    logger.info(f"访问 http://localhost:5001/api/clusters 获取容器集群监控数据")
    logger.info(f"访问 http://localhost:5001/health 进行健康检查")
    logger.info(f"访问 http://localhost:5001/cache-info 查看缓存状态")
    app.run(host='0.0.0.0', port=5001, debug=False)