tool.uv.package = true

[project]
name = "server"
version = "0.1.0"
description = "Example usage of the AG-UI adapter for Agno"
license = "MIT"

readme = "README.md"
requires-python = ">=3.12,<4.0"
dependencies = [
    "agno>=1.7.7",
    "openai>=1.99.1",
    "yfinance>=0.2.63",
    "fastapi>=0.116.1",
    "uvicorn>=0.35.0",
    "ag-ui-protocol>=0.1.8",
    "dotenv (>=0.9.9,<0.10.0)",
]
authors = [
    {name = "AG-UI Team"}
]


[project.scripts]
dev = "server:main"