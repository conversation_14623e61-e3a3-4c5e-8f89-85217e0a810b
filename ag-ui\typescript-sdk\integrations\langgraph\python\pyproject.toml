[tool.poetry]
name = "ag-ui-langgraph"
version = "0.0.10"
description = "Implementation of the AG-UI protocol for LangGraph."
authors = ["Ran <PERSON><PERSON> Tov <<EMAIL>>"]
readme = "README.md"
exclude = [
    "ag_ui_langgraph/examples/**",
]

[tool.poetry.dependencies]
python = "<3.14,>=3.10"
ag-ui-protocol = "==0.1.7"
fastapi = { version = "^0.115.12", optional = true }
langchain = ">=0.3.0"
langchain-core = ">=0.3.0"
langgraph = ">=0.3.25,<0.7.0"

[tool.poetry.extras]
fastapi = ["fastapi"]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
dev = "ag_ui_langgraph.dojo:main"